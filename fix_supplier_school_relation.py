"""
修复 SupplierSchoolRelation 模型的精度问题

此脚本使用系统自带的框架修复 SupplierSchoolRelation 模型的精度问题。
"""
from app import db, create_app
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_supplier_school_relation():
    """修复 SupplierSchoolRelation 模型的精度问题"""
    try:
        # 使用系统自带的会话执行SQL
        # 修复 supplier_school_relations 表的 created_at 和 updated_at 字段
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'supplier_school_relations' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE supplier_school_relations 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            ALTER TABLE supplier_school_relations 
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
            
            PRINT 'supplier_school_relations 表的 created_at 和 updated_at 字段已修复';
        END
        """))
        
        # 提交事务
        db.session.commit()
        logger.info("SupplierSchoolRelation 模型的精度问题已修复")
        return True
    except Exception as e:
        db.session.rollback()
        logger.error(f"修复 SupplierSchoolRelation 模型精度时出错: {str(e)}")
        return False

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        if fix_supplier_school_relation():
            print("成功修复 SupplierSchoolRelation 模型的精度问题")
        else:
            print("修复 SupplierSchoolRelation 模型精度时出错，请查看日志")
