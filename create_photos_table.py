from app import create_app, db
from app.models_daily_management import Photo
from sqlalchemy import text

app = create_app()

with app.app_context():
    # 检查表是否存在
    try:
        result = db.session.execute(text("SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'photos'"))
        exists = result.fetchone() is not None
        
        if exists:
            print("photos 表已存在")
        else:
            # 创建表
            print("创建 photos 表...")
            db.create_all()
            print("photos 表创建成功")
    except Exception as e:
        print(f"错误: {str(e)}")
