"""
修改数据库中的时间字段，将精度从0修改为1

此脚本将修改SQL Server数据库中的所有DateTime2类型字段，
将其精度从0修改为1，因为SQL Server不支持精度为0的DATETIME2类型。
"""

import pyodbc
import sys
import os
from datetime import datetime

# 配置信息
SQL_SERVER = 'localhost\\SQLEXPRESS'
SQL_SERVER_DB = 'StudentsCMSSP'

def get_sqlserver_connection():
    """获取SQL Server数据库连接"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={SQL_SERVER};DATABASE={SQL_SERVER_DB};Trusted_Connection=yes"
        conn = pyodbc.connect(conn_str)
        print(f"已成功连接到SQL Server数据库: {SQL_SERVER_DB}")
        return conn
    except pyodbc.Error as e:
        print(f"连接SQL Server数据库时出错: {e}")
        return None

def get_datetime2_columns(conn):
    """获取所有DATETIME2类型的列"""
    cursor = conn.cursor()
    cursor.execute("""
    SELECT
        t.name AS table_name,
        c.name AS column_name,
        ty.name AS data_type
    FROM
        sys.columns c
    JOIN
        sys.tables t ON c.object_id = t.object_id
    JOIN
        sys.types ty ON c.user_type_id = ty.user_type_id
    WHERE
        ty.name = 'datetime2'
    ORDER BY
        t.name, c.column_id
    """)

    datetime_columns = cursor.fetchall()
    cursor.close()
    return datetime_columns

def fix_datetime_precision(conn, table_name, column_name, precision=1):
    """修改日期时间字段的精度为1（最小有效精度）"""
    cursor = conn.cursor()

    try:
        # 检查表是否存在
        cursor.execute(f"""
        IF EXISTS (SELECT 1 FROM sys.tables WHERE name = '{table_name}')
        SELECT 1
        ELSE
        SELECT 0
        """)
        
        if cursor.fetchone()[0] == 0:
            print(f"表 {table_name} 不存在，跳过")
            return False
            
        # 检查列是否存在
        cursor.execute(f"""
        IF EXISTS (
            SELECT 1 FROM sys.columns 
            WHERE object_id = OBJECT_ID('{table_name}') 
            AND name = '{column_name}'
        )
        SELECT 1
        ELSE
        SELECT 0
        """)
        
        if cursor.fetchone()[0] == 0:
            print(f"列 {table_name}.{column_name} 不存在，跳过")
            return False

        # 检查列是否允许为空
        cursor.execute(f"""
        SELECT 
            IS_NULLABLE 
        FROM 
            INFORMATION_SCHEMA.COLUMNS 
        WHERE 
            TABLE_NAME = '{table_name}' 
            AND COLUMN_NAME = '{column_name}'
        """)
        
        is_nullable = cursor.fetchone()[0]
        nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
        
        # 修改列的数据类型
        print(f"正在修改 {table_name}.{column_name} 的精度为 {precision}...")
        cursor.execute(f"""
        ALTER TABLE {table_name}
        ALTER COLUMN {column_name} DATETIME2({precision}) {nullable}
        """)
        
        conn.commit()
        print(f"已成功修改 {table_name}.{column_name} 的精度为 {precision}")
        return True
    except pyodbc.Error as e:
        conn.rollback()
        print(f"修改 {table_name}.{column_name} 时出错: {e}")
        return False
    finally:
        cursor.close()

def fix_all_datetime2_columns(conn, precision=1):
    """修改所有DATETIME2字段的精度"""
    datetime_columns = get_datetime2_columns(conn)
    print(f"找到 {len(datetime_columns)} 个DATETIME2类型的列")

    success_count = 0
    error_count = 0

    for table_name, column_name, data_type in datetime_columns:
        print(f"\n处理 {table_name}.{column_name} ({data_type})")
        if fix_datetime_precision(conn, table_name, column_name, precision):
            success_count += 1
        else:
            error_count += 1

    print(f"\n修改完成: 成功 {success_count} 个, 失败 {error_count} 个")
    return success_count, error_count

def main():
    """主函数"""
    conn = get_sqlserver_connection()
    if not conn:
        print("无法连接到数据库，退出程序")
        sys.exit(1)

    try:
        print("开始修改数据库中的时间字段精度...")
        fix_all_datetime2_columns(conn, precision=1)
        print("\n所有时间字段已修改为精度为1（精确到分钟）")
    finally:
        conn.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main()
