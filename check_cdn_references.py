#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查项目中是否还有使用 CDN 的地方
"""

import os
import re
import sys
from colorama import init, Fore, Style

# 初始化 colorama
init()

# CDN 域名列表
CDN_PATTERNS = [
    "cdn.jsdelivr.net",
    "cdnjs.cloudflare.com",
    "code.jquery.com",
    "fonts.googleapis.com",
    "maxcdn.bootstrapcdn.com",
    "stackpath.bootstrapcdn.com",
    "unpkg.com",
    "cdn.datatables.net"
]

# 要检查的文件扩展名
FILE_EXTENSIONS = ['.html', '.js', '.css', '.py']

# 要检查的目录
DIRECTORIES = ['app/templates', 'app/static']

# 要排除的目录
EXCLUDE_DIRS = ['venv', 'node_modules', 'bootstrap-4.6.2', 'bootstrap-4.6.2.BAK', '__pycache__']

def check_file(file_path):
    """检查单个文件中的 CDN 引用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except UnicodeDecodeError:
            print(f"{Fore.YELLOW}警告: 无法读取文件 {file_path} (编码问题){Style.RESET_ALL}")
            return []
    
    found_references = []
    for pattern in CDN_PATTERNS:
        if pattern in content:
            # 使用正则表达式找到包含 CDN 的完整行
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if pattern in line:
                    found_references.append((pattern, i+1, line.strip()))
    
    return found_references

def scan_directory(directory):
    """递归扫描目录中的文件"""
    found_files = []
    
    for root, dirs, files in os.walk(directory):
        # 排除指定目录
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
        
        for file in files:
            if any(file.endswith(ext) for ext in FILE_EXTENSIONS):
                file_path = os.path.join(root, file)
                references = check_file(file_path)
                if references:
                    found_files.append((file_path, references))
    
    return found_files

def main():
    """主函数"""
    print(f"{Fore.CYAN}开始检查项目中的 CDN 引用...{Style.RESET_ALL}")
    
    all_found_files = []
    for directory in DIRECTORIES:
        if os.path.exists(directory):
            print(f"{Fore.BLUE}正在检查目录: {directory}{Style.RESET_ALL}")
            found_files = scan_directory(directory)
            all_found_files.extend(found_files)
        else:
            print(f"{Fore.YELLOW}警告: 目录 {directory} 不存在{Style.RESET_ALL}")
    
    if all_found_files:
        print(f"\n{Fore.RED}发现 {len(all_found_files)} 个文件中包含 CDN 引用:{Style.RESET_ALL}")
        for file_path, references in all_found_files:
            print(f"\n{Fore.GREEN}文件: {file_path}{Style.RESET_ALL}")
            for pattern, line_num, line in references:
                print(f"  {Fore.YELLOW}行 {line_num}: {Style.RESET_ALL}{line}")
        
        print(f"\n{Fore.RED}总计: {len(all_found_files)} 个文件中包含 CDN 引用{Style.RESET_ALL}")
        return 1
    else:
        print(f"\n{Fore.GREEN}恭喜! 未发现任何 CDN 引用。项目已完全本地化。{Style.RESET_ALL}")
        return 0

if __name__ == "__main__":
    sys.exit(main())
