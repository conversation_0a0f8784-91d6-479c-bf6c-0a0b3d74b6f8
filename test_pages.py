#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试所有页面是否能正常加载
"""

import os
import sys
import time
import argparse
import requests
from urllib.parse import urljoin
from colorama import init, Fore, Style
from bs4 import BeautifulSoup

# 初始化 colorama
init()

# 默认基础 URL
DEFAULT_BASE_URL = "http://127.0.0.1:5000"

# 要测试的路由列表
ROUTES_TO_TEST = [
    "/",
    "/login",
    "/dashboard",
    "/employee",
    "/employee/add",
    "/area",
    "/area/add",
    "/warehouse",
    "/warehouse/add",
    "/supplier",
    "/supplier/add",
    "/ingredient",
    "/ingredient/add",
    "/ingredient/categories",
    "/recipe",
    "/recipe/add",
    "/menu_plan",
    "/menu_plan/add",
    "/menu_plan/week",
    "/purchase_order",
    "/purchase_order/add",
    "/stock_in",
    "/stock_in/add",
    "/stock_out",
    "/stock_out/add",
    "/consumption_plan",
    "/consumption_plan/add",
    "/food_sample",
    "/food_sample/add",
    "/daily_management/companions",
    "/daily_management/inspections",
    "/daily_management/trainings",
    "/daily_management/events",
    "/daily_management/issues"
]

def test_page(session, base_url, route, check_resources=False):
    """测试单个页面是否能正常加载"""
    url = urljoin(base_url, route)
    try:
        response = session.get(url, timeout=10)
        if response.status_code == 200:
            print(f"{Fore.GREEN}✓ {url} - 页面加载成功{Style.RESET_ALL}")
            
            if check_resources:
                # 检查页面中的资源是否能正常加载
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 检查 CSS 文件
                css_files = []
                for link in soup.find_all('link'):
                    if link.get('rel') and 'stylesheet' in link.get('rel') and link.get('href'):
                        css_files.append(link.get('href'))
                
                # 检查 JavaScript 文件
                js_files = []
                for script in soup.find_all('script'):
                    if script.get('src'):
                        js_files.append(script.get('src'))
                
                # 检查图片文件
                img_files = []
                for img in soup.find_all('img'):
                    if img.get('src'):
                        img_files.append(img.get('src'))
                
                # 合并所有资源
                all_resources = css_files + js_files + img_files
                
                # 过滤掉外部资源
                local_resources = [res for res in all_resources if not res.startswith('http')]
                
                print(f"  发现 {len(local_resources)} 个本地资源")
                
                # 测试本地资源是否能正常加载
                for resource in local_resources:
                    resource_url = urljoin(url, resource)
                    try:
                        resource_response = session.get(resource_url, timeout=5)
                        if resource_response.status_code == 200:
                            print(f"    {Fore.GREEN}✓ {resource} - 资源加载成功{Style.RESET_ALL}")
                        else:
                            print(f"    {Fore.RED}✗ {resource} - 资源加载失败 (状态码: {resource_response.status_code}){Style.RESET_ALL}")
                    except Exception as e:
                        print(f"    {Fore.RED}✗ {resource} - 资源加载失败: {str(e)}{Style.RESET_ALL}")
            
            return True
        else:
            print(f"{Fore.RED}✗ {url} - 页面加载失败 (状态码: {response.status_code}){Style.RESET_ALL}")
            return False
    except Exception as e:
        print(f"{Fore.RED}✗ {url} - 页面加载失败: {str(e)}{Style.RESET_ALL}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试所有页面是否能正常加载')
    parser.add_argument('--base-url', default=DEFAULT_BASE_URL, help='基础 URL')
    parser.add_argument('--check-resources', action='store_true', help='检查页面中的资源是否能正常加载')
    parser.add_argument('--login', action='store_true', help='尝试登录')
    parser.add_argument('--username', default='admin', help='登录用户名')
    parser.add_argument('--password', default='password', help='登录密码')
    args = parser.parse_args()
    
    print(f"{Fore.CYAN}开始测试页面加载...{Style.RESET_ALL}")
    print(f"基础 URL: {args.base_url}")
    print(f"检查资源: {'是' if args.check_resources else '否'}")
    print(f"尝试登录: {'是' if args.login else '否'}")
    
    # 创建会话
    session = requests.Session()
    
    # 尝试登录
    if args.login:
        login_url = urljoin(args.base_url, '/login')
        try:
            # 获取登录页面以获取 CSRF 令牌
            response = session.get(login_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_token = soup.find('input', {'name': 'csrf_token'}).get('value')
            
            # 提交登录表单
            login_data = {
                'csrf_token': csrf_token,
                'username': args.username,
                'password': args.password
            }
            response = session.post(login_url, data=login_data, allow_redirects=True)
            
            if response.url != login_url:
                print(f"{Fore.GREEN}✓ 登录成功{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}✗ 登录失败{Style.RESET_ALL}")
                return 1
        except Exception as e:
            print(f"{Fore.RED}✗ 登录失败: {str(e)}{Style.RESET_ALL}")
            return 1
    
    # 测试所有页面
    success_count = 0
    failure_count = 0
    
    for route in ROUTES_TO_TEST:
        if test_page(session, args.base_url, route, args.check_resources):
            success_count += 1
        else:
            failure_count += 1
        
        # 添加延迟，避免请求过快
        time.sleep(0.5)
    
    # 打印测试结果
    print(f"\n{Fore.CYAN}测试完成!{Style.RESET_ALL}")
    print(f"成功: {Fore.GREEN}{success_count}{Style.RESET_ALL}")
    print(f"失败: {Fore.RED}{failure_count}{Style.RESET_ALL}")
    print(f"总计: {success_count + failure_count}")
    
    return 0 if failure_count == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
