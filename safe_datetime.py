"""
安全的datetime处理

提供安全的datetime处理函数和装饰器，解决SQL Server DATETIME2精度问题。
"""
from datetime import datetime
from functools import wraps
import inspect
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"safe_datetime_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def safe_datetime(dt=None):
    """
    创建一个安全的datetime对象，用于SQL Server数据库操作。
    
    Args:
        dt: 要处理的datetime对象，如果为None则使用当前时间
        
    Returns:
        处理后的datetime对象，确保微秒部分为0
    """
    if dt is None:
        dt = datetime.now()
    return dt.replace(microsecond=0)

def safe_datetime_decorator(func):
    """
    装饰器，确保函数返回的datetime对象是安全的。
    
    Args:
        func: 要装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        if isinstance(result, datetime):
            return result.replace(microsecond=0)
        return result
    return wrapper

def patch_datetime_now():
    """
    修补datetime.now函数，确保返回的datetime对象是安全的。
    """
    original_now = datetime.now
    
    @wraps(original_now)
    def safe_now():
        return original_now().replace(microsecond=0)
    
    datetime.now = safe_now
    logger.info("已修补datetime.now函数")

def patch_sqlalchemy_datetime():
    """
    修补SQLAlchemy的DateTime类型，确保使用正确的精度。
    """
    try:
        from sqlalchemy import DateTime
        from sqlalchemy.dialects.mssql import DATETIME2
        
        # 保存原始类
        original_datetime = DateTime
        original_datetime2 = DATETIME2
        
        # 创建带有默认精度的DateTime类
        class SafeDateTime(DateTime):
            def __init__(self, precision=1, **kw):
                super().__init__(precision=precision, **kw)
        
        # 创建带有默认精度的DATETIME2类
        class SafeDatetime2(DATETIME2):
            def __init__(self, precision=1, **kw):
                super().__init__(precision=precision, **kw)
        
        # 替换原始类
        DateTime = SafeDateTime
        DATETIME2 = SafeDatetime2
        
        # 修补SQLAlchemy模块
        import sqlalchemy
        sqlalchemy.DateTime = SafeDateTime
        
        # 修补SQLAlchemy方言模块
        import sqlalchemy.dialects.mssql
        sqlalchemy.dialects.mssql.DATETIME2 = SafeDatetime2
        
        logger.info("已修补SQLAlchemy的DateTime类型")
        return True
    except ImportError:
        logger.warning("未找到SQLAlchemy模块，跳过修补")
        return False
    except Exception as e:
        logger.error(f"修补SQLAlchemy的DateTime类型时出错: {e}")
        return False

def create_model_initializer():
    """
    创建模型初始化器，用于在模型创建时确保datetime字段使用安全的值。
    """
    try:
        with open('app/utils/model_initializer.py', 'w', encoding='utf-8') as f:
            f.write("""\"\"\"
模型初始化器

用于在模型创建时确保datetime字段使用安全的值。
\"\"\"
from datetime import datetime

def safe_datetime(dt=None):
    \"\"\"
    创建一个安全的datetime对象，用于SQL Server数据库操作。
    
    Args:
        dt: 要处理的datetime对象，如果为None则使用当前时间
        
    Returns:
        处理后的datetime对象，确保微秒部分为0
    \"\"\"
    if dt is None:
        dt = datetime.now()
    return dt.replace(microsecond=0)

def initialize_model(model):
    \"\"\"
    初始化模型，确保datetime字段使用安全的值。
    
    Args:
        model: 要初始化的模型实例
        
    Returns:
        初始化后的模型实例
    \"\"\"
    for attr_name in dir(model):
        # 跳过私有属性和方法
        if attr_name.startswith('_'):
            continue
        
        try:
            attr_value = getattr(model, attr_name)
            # 如果属性是datetime对象，确保微秒部分为0
            if isinstance(attr_value, datetime):
                setattr(model, attr_name, attr_value.replace(microsecond=0))
        except:
            # 忽略无法获取的属性
            pass
    
    return model
""")
        logger.info("已创建模型初始化器")
        return True
    except Exception as e:
        logger.error(f"创建模型初始化器时出错: {e}")
        return False

def create_safe_datetime_module():
    """
    创建安全的datetime模块，用于替换标准的datetime模块。
    """
    try:
        with open('app/utils/safe_datetime.py', 'w', encoding='utf-8') as f:
            f.write("""\"\"\"
安全的datetime模块

提供安全的datetime处理函数，解决SQL Server DATETIME2精度问题。
\"\"\"
from datetime import datetime as _datetime
from datetime import date, time, timedelta, timezone

# 重新导出标准模块的所有内容
date = date
time = time
timedelta = timedelta
timezone = timezone

# 创建安全的datetime类
class datetime(_datetime):
    @classmethod
    def now(cls, tz=None):
        \"\"\"安全的now方法，确保微秒部分为0\"\"\"
        dt = _datetime.now(tz)
        return dt.replace(microsecond=0)
    
    @classmethod
    def utcnow(cls):
        \"\"\"安全的utcnow方法，确保微秒部分为0\"\"\"
        dt = _datetime.utcnow()
        return dt.replace(microsecond=0)
    
    def __new__(cls, *args, **kwargs):
        \"\"\"创建新的datetime对象时确保微秒部分为0\"\"\"
        instance = _datetime.__new__(cls, *args, **kwargs)
        return instance.replace(microsecond=0)

# 辅助函数
def safe_datetime(dt=None):
    \"\"\"创建安全的datetime对象\"\"\"
    if dt is None:
        dt = _datetime.now()
    return dt.replace(microsecond=0)

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    \"\"\"格式化datetime对象\"\"\"
    if dt is None:
        return ''
    return dt.strftime(format_str)
""")
        logger.info("已创建安全的datetime模块")
        return True
    except Exception as e:
        logger.error(f"创建安全的datetime模块时出错: {e}")
        return False

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "安全的datetime处理")
    print("="*80)
    
    # 修补datetime.now函数
    patch_datetime_now()
    
    # 修补SQLAlchemy的DateTime类型
    patch_sqlalchemy_datetime()
    
    # 创建模型初始化器
    create_model_initializer()
    
    # 创建安全的datetime模块
    create_safe_datetime_module()
    
    print("\n所有修补已完成，请在代码中使用安全的datetime函数")
    print("\n示例:")
    print("  from app.utils.safe_datetime import safe_datetime")
    print("  dt = safe_datetime()  # 返回安全的当前时间")
    print("\n或者:")
    print("  from app.utils.model_initializer import initialize_model")
    print("  model = initialize_model(model)  # 初始化模型的datetime字段")
    print("\n" + "="*80)

if __name__ == "__main__":
    main()
