{{- /*
  Usage: `placeholder args`

  `args` are all optional and can be one of the following:
    * title: Used in the SVG `title` tag - default: "Placeholder"
    * text: The text to show in the image - default: "width x height"
    * class: Class to add to the `svg` - default: "bd-placeholder-img"
    * color: The text color (foreground) - default: "#dee2e6"
    * background: The background color - default: "#868e96"
    * width: default: "100%"
    * height: default: "180px"
*/ -}}

{{- $grays := $.Site.Data.grays -}}
{{- $default_color := (index $grays 2).hex -}}
{{- $default_background := (index $grays 5).hex -}}

{{- $title := .Get "title" | default "Placeholder" -}}
{{- $class := .Get "class" -}}
{{- $color := .Get "color" | default $default_color -}}
{{- $background := .Get "background" | default $default_background -}}
{{- $width := .Get "width" | default "100%" -}}
{{- $height := .Get "height" | default "180" -}}
{{- $text := .Get "text" | default (printf "%sx%s" $width $height) -}}

{{- $show_title := not (eq $title "false") -}}
{{- $show_text := not (eq $text "false") -}}

<svg class="bd-placeholder-img{{ with $class }} {{ . }}{{ end }}" width="{{ $width }}" height="{{ $height }}" xmlns="http://www.w3.org/2000/svg"{{ if (or $show_title $show_text) }} role="img" aria-label="{{ if $show_title }}{{ $title }}{{ if $show_text }}: {{ end }}{{ end }}{{ if ($show_text) }}{{ $text }}{{ end }}"{{ else }} aria-hidden="true"{{ end }} preserveAspectRatio="xMidYMid slice" focusable="false">
  {{- if $show_title }}<title>{{ $title }}</title>{{ end -}}
  <rect width="100%" height="100%" fill="{{ $background }}"/>
  {{- if $show_text }}<text x="50%" y="50%" fill="{{ $color }}" dy=".3em">{{ $text }}</text>{{ end -}}
</svg>
