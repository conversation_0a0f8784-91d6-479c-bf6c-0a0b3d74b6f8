"""
修复audit_logs表的created_at字段精度
"""

import pyodbc
import sys

# 配置信息
SQL_SERVER = 'localhost\\SQLEXPRESS'
SQL_SERVER_DB = 'StudentsCMSSP'

def get_sqlserver_connection():
    """获取SQL Server数据库连接"""
    try:
        conn_str = f'DRIVER={{SQL Server}};SERVER={SQL_SERVER};DATABASE={SQL_SERVER_DB};Trusted_Connection=yes;'
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        print(f"错误: 无法连接到SQL Server: {e}")
        return None

def fix_audit_logs_datetime(conn, precision=6):
    """修复audit_logs表的created_at字段精度"""
    cursor = conn.cursor()
    
    try:
        # 清空表数据
        cursor.execute("DELETE FROM audit_logs")
        conn.commit()
        print("已清空audit_logs表的所有数据")
        
        # 修改列的数据类型
        cursor.execute(f"""
        ALTER TABLE audit_logs
        ALTER COLUMN created_at DATETIME2({precision}) NOT NULL
        """)
        conn.commit()
        print(f"已修改audit_logs.created_at的数据类型为DATETIME2({precision})")
        
        return True
    except pyodbc.Error as e:
        conn.rollback()
        print(f"修改表结构时出错: {e}")
        return False
    finally:
        cursor.close()

def main():
    """主函数"""
    conn = get_sqlserver_connection()
    if not conn:
        sys.exit(1)
    
    # 修复audit_logs表的created_at字段
    print("修复audit_logs表的created_at字段")
    if fix_audit_logs_datetime(conn):
        print("修复成功")
    else:
        print("修复失败")
    
    conn.close()

if __name__ == "__main__":
    main()
