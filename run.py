from app import create_app
from app.utils.ssl_handler import get_ssl_context, log_ssl_info

app = create_app()

if __name__ == '__main__':
    # Get host and port from configuration
    host = app.config.get('HOST', '0.0.0.0')
    port = app.config.get('PORT', 5000)

    # Get SSL context from configuration
    ssl_context = get_ssl_context(app)

    # Log SSL information
    log_ssl_info(app, host, port, ssl_context)

    app.run(debug=1, host=host, port=port, ssl_context=ssl_context)
