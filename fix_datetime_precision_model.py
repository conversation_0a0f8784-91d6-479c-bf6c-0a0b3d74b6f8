"""
修复模型中的DATETIME2精度问题

此脚本用于修复SQLAlchemy模型中的DATETIME2精度问题，
确保在创建datetime对象时指定正确的精度。

错误信息：
DBAPIError: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
"""
import os
import sys
import re
import logging
from datetime import datetime

# 配置日志
log_file = f"datetime_precision_model_fix_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 模型文件路径
MODEL_PATHS = [
    'app/models.py',
    'app/models_daily_management.py',
    'app/models_phase4.py',
    'app/models/inventory.py',
    'app/models/employee.py',
    'venv/app/models.py',
    'venv/app/models_daily_management.py',
    'venv/app/models_phase4.py',
    'venv/app/models/inventory.py',
    'venv/app/models/employee.py'
]

def fix_model_file(file_path):
    """修复模型文件中的DATETIME2精度问题"""
    if not os.path.exists(file_path):
        logger.warning(f"文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已备份原文件: {backup_path}")
        
        # 修复 db.DateTime 没有指定精度的情况
        pattern1 = r'db\.DateTime\s*\('
        replacement1 = 'db.DateTime(precision=1, '
        content_fixed = re.sub(pattern1, replacement1, content)
        
        # 修复 db.DateTime 没有括号的情况
        pattern2 = r'db\.DateTime(?!\s*\()'
        replacement2 = 'db.DateTime(precision=1)'
        content_fixed = re.sub(pattern2, replacement2, content_fixed)
        
        # 修复 DATETIME2 没有指定精度的情况
        pattern3 = r'DATETIME2\s*\('
        replacement3 = 'DATETIME2(precision=1, '
        content_fixed = re.sub(pattern3, replacement3, content_fixed)
        
        # 修复 DATETIME2 没有括号的情况
        pattern4 = r'DATETIME2(?!\s*\()'
        replacement4 = 'DATETIME2(precision=1)'
        content_fixed = re.sub(pattern4, replacement4, content_fixed)
        
        # 修复 datetime.now() 没有替换微秒的情况
        pattern5 = r'datetime\.now\(\)'
        replacement5 = 'datetime.now().replace(microsecond=0)'
        content_fixed = re.sub(pattern5, replacement5, content_fixed)
        
        # 写入修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content_fixed)
        
        logger.info(f"已修复文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"修复文件 {file_path} 时出错: {e}")
        return False

def create_safe_datetime_helper():
    """创建安全的datetime处理辅助函数"""
    helper_path = 'app/utils/datetime_helper.py'
    
    # 确保目录存在
    os.makedirs(os.path.dirname(helper_path), exist_ok=True)
    
    try:
        with open(helper_path, 'w', encoding='utf-8') as f:
            f.write("""\"\"\"
日期时间处理辅助函数

提供安全的日期时间处理函数，解决SQL Server DATETIME2精度问题。
\"\"\"
from datetime import datetime

def safe_datetime_for_db(dt=None):
    \"\"\"
    创建一个安全的datetime对象，用于SQL Server数据库操作。
    
    Args:
        dt: 要处理的datetime对象，如果为None则使用当前时间
        
    Returns:
        处理后的datetime对象，确保微秒部分为0
    \"\"\"
    if dt is None:
        dt = datetime.now()
    return dt.replace(microsecond=0)

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    \"\"\"
    格式化datetime对象为字符串
    
    Args:
        dt: 要格式化的datetime对象
        format_str: 格式化字符串
        
    Returns:
        格式化后的字符串，如果dt为None则返回空字符串
    \"\"\"
    if dt is None:
        return ''
    return dt.strftime(format_str)
""")
        logger.info(f"已创建datetime辅助函数: {helper_path}")
        return True
    except Exception as e:
        logger.error(f"创建datetime辅助函数时出错: {e}")
        return False

def create_model_patch():
    """创建模型补丁文件"""
    patch_path = 'app/utils/model_patch.py'
    
    # 确保目录存在
    os.makedirs(os.path.dirname(patch_path), exist_ok=True)
    
    try:
        with open(patch_path, 'w', encoding='utf-8') as f:
            f.write("""\"\"\"
模型补丁

用于修复SQLAlchemy模型中的DATETIME2精度问题。
在应用启动时应用此补丁。
\"\"\"
from sqlalchemy.dialects.mssql import DATETIME2
from sqlalchemy import DateTime
from datetime import datetime

# 保存原始的类
original_datetime2 = DATETIME2
original_datetime = DateTime

# 创建带有默认精度的DATETIME2类
class SafeDatetime2(DATETIME2):
    def __init__(self, precision=1, **kw):
        super().__init__(precision=precision, **kw)

# 创建带有默认精度的DateTime类
class SafeDateTime(DateTime):
    def __init__(self, precision=1, **kw):
        super().__init__(precision=precision, **kw)

# 替换原始类
DATETIME2 = SafeDatetime2
DateTime = SafeDateTime

def apply_patch():
    \"\"\"应用补丁，替换SQLAlchemy中的类\"\"\"
    from sqlalchemy.dialects.mssql import base
    base.DATETIME2 = SafeDatetime2
    
    from sqlalchemy import sql
    sql.sqltypes.DateTime = SafeDateTime
    
    print("已应用DATETIME2精度补丁")

def safe_datetime():
    \"\"\"返回安全的datetime对象\"\"\"
    return datetime.now().replace(microsecond=0)
""")
        logger.info(f"已创建模型补丁文件: {patch_path}")
        return True
    except Exception as e:
        logger.error(f"创建模型补丁文件时出错: {e}")
        return False

def create_app_patch():
    """创建应用补丁文件，在应用启动时应用模型补丁"""
    patch_path = 'app/utils/app_patch.py'
    
    # 确保目录存在
    os.makedirs(os.path.dirname(patch_path), exist_ok=True)
    
    try:
        with open(patch_path, 'w', encoding='utf-8') as f:
            f.write("""\"\"\"
应用补丁

在应用启动时应用各种补丁。
\"\"\"

def apply_patches(app):
    \"\"\"应用所有补丁\"\"\"
    # 应用DATETIME2精度补丁
    try:
        from app.utils.model_patch import apply_patch
        apply_patch()
        app.logger.info("已应用DATETIME2精度补丁")
    except Exception as e:
        app.logger.error(f"应用DATETIME2精度补丁时出错: {e}")
    
    return app
""")
        logger.info(f"已创建应用补丁文件: {patch_path}")
        return True
    except Exception as e:
        logger.error(f"创建应用补丁文件时出错: {e}")
        return False

def update_app_init():
    """更新app/__init__.py文件，应用补丁"""
    init_path = 'app/__init__.py'
    
    if not os.path.exists(init_path):
        logger.warning(f"文件不存在: {init_path}")
        return False
    
    try:
        with open(init_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = f"{init_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已备份原文件: {backup_path}")
        
        # 查找create_app函数
        if 'def create_app(' in content:
            # 在create_app函数末尾添加补丁应用代码
            pattern = r'(def create_app\([^)]*\):.*?)(return app)'
            replacement = r'\1    # 应用补丁\n    from app.utils.app_patch import apply_patches\n    app = apply_patches(app)\n\n    \2'
            content_fixed = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            # 写入修复后的内容
            with open(init_path, 'w', encoding='utf-8') as f:
                f.write(content_fixed)
            
            logger.info(f"已更新应用初始化文件: {init_path}")
            return True
        else:
            logger.warning(f"在 {init_path} 中未找到 create_app 函数")
            return False
    except Exception as e:
        logger.error(f"更新应用初始化文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "修复模型中的DATETIME2精度问题")
    print("="*80)
    print(f"\n日志文件: {log_file}")
    
    # 创建辅助函数和补丁
    create_safe_datetime_helper()
    create_model_patch()
    create_app_patch()
    update_app_init()
    
    # 修复模型文件
    fixed_count = 0
    for file_path in MODEL_PATHS:
        if fix_model_file(file_path):
            fixed_count += 1
    
    print(f"\n已修复 {fixed_count} 个模型文件")
    print("\n请重启应用以应用补丁")
    print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
