-- 开始事务
BEGIN TRANSACTION;

-- 检查是否存在调味品分类
DECLARE @condiment_category_id INT;
SELECT @condiment_category_id = id FROM ingredient_categories WHERE name = '调味品';

-- 如果不存在，则创建调味品分类
IF @condiment_category_id IS NULL
BEGIN
    INSERT INTO ingredient_categories (name, description, created_at)
    VALUES ('调味品', '各类调味品', GETDATE());
    
    SET @condiment_category_id = SCOPE_IDENTITY();
    PRINT '已创建调味品分类，ID为: ' + CAST(@condiment_category_id AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '调味品分类已存在，ID为: ' + CAST(@condiment_category_id AS NVARCHAR(10));
END

-- 更新所有标记为调味品的食材，关联到调味品分类
UPDATE ingredients
SET category_id = @condiment_category_id
WHERE is_condiment = 1 AND (category_id IS NULL OR category_id <> @condiment_category_id);

-- 提交事务
COMMIT;

PRINT '调味品分类设置完成';
