-- 开始事务
BEGIN TRANSACTION;

-- 修改menu_plan_id字段，使其可以为空
IF EXISTS (
    SELECT * 
    FROM sys.columns 
    WHERE object_id = OBJECT_ID('consumption_plans') 
    AND name = 'menu_plan_id' 
    AND is_nullable = 0
)
BEGIN
    -- 获取外键约束名称
    DECLARE @fk_name NVARCHAR(128);
    SELECT @fk_name = name
    FROM sys.foreign_keys
    WHERE parent_object_id = OBJECT_ID('consumption_plans')
    AND referenced_object_id = OBJECT_ID('menu_plans')
    AND parent_column_id = (
        SELECT column_id
        FROM sys.columns
        WHERE object_id = OBJECT_ID('consumption_plans')
        AND name = 'menu_plan_id'
    );

    -- 如果存在外键约束，先删除
    IF @fk_name IS NOT NULL
    BEGIN
        DECLARE @sql NVARCHAR(MAX) = N'ALTER TABLE consumption_plans DROP CONSTRAINT ' + @fk_name;
        EXEC sp_executesql @sql;
        PRINT '已删除外键约束: ' + @fk_name;
    END

    -- 修改字段为可空
    ALTER TABLE consumption_plans ALTER COLUMN menu_plan_id INT NULL;
    PRINT 'menu_plan_id字段已修改为可空';

    -- 重新添加外键约束
    IF @fk_name IS NOT NULL
    BEGIN
        ALTER TABLE consumption_plans
        ADD CONSTRAINT FK_consumption_plans_menu_plans
        FOREIGN KEY (menu_plan_id) REFERENCES menu_plans(id);
        PRINT '已重新添加外键约束';
    END
END
ELSE
BEGIN
    PRINT 'menu_plan_id字段已经是可空的，无需修改';
END

-- 验证修改结果
SELECT 
    COLUMN_NAME, 
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'consumption_plans' 
AND COLUMN_NAME = 'menu_plan_id';

-- 提交事务
COMMIT;

PRINT '表修改完成';
