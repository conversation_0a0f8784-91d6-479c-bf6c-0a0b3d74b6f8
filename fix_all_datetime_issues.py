"""
全面修复项目中的datetime精度问题

此脚本将：
1. 修复所有模型中的db.DateTime字段，确保它们都指定了精度为1
2. 修复所有使用datetime.now()作为默认值的地方，使用lambda: datetime.now().replace(microsecond=0)
3. 生成一个SQL脚本来修改数据库中所有datetime2(0)类型的列为datetime2(1)
4. 修复所有直接使用ORM创建对象的地方，如果包含datetime字段，使用replace(microsecond=0)
5. 为特定的表（如area_change_history）创建辅助函数，使用原始SQL插入记录
"""

import os
import re
import sys
import time
from datetime import datetime

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 模型文件列表
MODEL_FILES = [
    "app/models.py",
    "app/models_phase1.py",
    "app/models_phase2.py",
    "app/models_phase3.py",
    "app/models_phase4.py",
    "app/models_recipe_advanced.py",
    "app/models_ingredient_traceability.py",
    "app/models_supplier.py",
    "app/models/notification.py"
]

# 需要使用原始SQL插入的表
TABLES_NEED_RAW_SQL = [
    "area_change_history",
    "audit_log"
]

def backup_file(file_path):
    """备份文件"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        try:
            with open(file_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            print(f"已备份文件: {file_path} -> {backup_path}")
            return True
        except Exception as e:
            print(f"备份文件时出错: {str(e)}")
            return False
    return False

def fix_datetime_in_model_file(file_path, precision=1):
    """修复模型文件中的DateTime字段定义"""
    try:
        full_path = os.path.join(PROJECT_ROOT, file_path)
        if not os.path.exists(full_path):
            print(f"文件不存在: {full_path}")
            return 0

        # 备份文件
        if not backup_file(full_path):
            print(f"备份文件失败，跳过修复: {full_path}")
            return 0

        # 读取文件内容
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 修复1: db.Column(db.DateTime, ...) -> db.Column(DATETIME2(precision=1), ...)
        pattern1 = r'db\.Column\(db\.DateTime(?!\s*\()'
        replacement1 = f'db.Column(db.DateTime({precision})'
        content = re.sub(pattern1, replacement1, content)

        # 修复2: default=datetime.now -> default=lambda: datetime.now().replace(microsecond=0)
        pattern2 = r'default=datetime\.now'
        replacement2 = r'default=lambda: datetime.now().replace(microsecond=0)'
        content = re.sub(pattern2, replacement2, content)

        # 修复3: onupdate=datetime.now -> onupdate=lambda: datetime.now().replace(microsecond=0)
        pattern3 = r'onupdate=datetime\.now'
        replacement3 = r'onupdate=lambda: datetime.now().replace(microsecond=0)'
        content = re.sub(pattern3, replacement3, content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改文件: {file_path}")
            return 1
        else:
            print(f"文件无需修改: {file_path}")
            return 0

    except Exception as e:
        print(f"修改文件时出错: {str(e)}")
        return 0

def generate_sql_script():
    """生成SQL脚本来修改数据库中的datetime2字段精度"""
    try:
        sql_file = os.path.join(PROJECT_ROOT, 'fix_datetime_precision.sql')
        
        # SQL脚本内容
        sql_content = """-- 修复日期时间精度问题
-- 这个脚本将修改所有表中的datetime2字段，将其精度设置为1

-- 获取所有表中的datetime2字段
DECLARE @sql NVARCHAR(MAX) = '';

-- 构建修改语句
SELECT @sql = @sql + 
    'IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''[dbo].[' + t.name + ']'') AND name = ''' + c.name + ''' AND system_type_id = 42) ' +
    'BEGIN ' +
    'PRINT ''修改表 ' + t.name + ' 中的字段 ' + c.name + ' 的精度为1''; ' +
    'ALTER TABLE [dbo].[' + t.name + '] ALTER COLUMN [' + c.name + '] DATETIME2(1) ' + 
    CASE WHEN c.is_nullable = 0 THEN 'NOT NULL' ELSE 'NULL' END + '; ' +
    'END; '
FROM sys.tables t
JOIN sys.columns c ON t.object_id = c.object_id
JOIN sys.types ty ON c.system_type_id = ty.system_type_id
WHERE t.is_ms_shipped = 0 -- 非系统表
AND ty.name = 'datetime2'
AND c.max_length = 8; -- datetime2(0)的长度是8字节

-- 执行动态SQL
PRINT '开始修改datetime2字段的精度...';
EXEC sp_executesql @sql;
PRINT '修改完成';
"""
        
        # 写入SQL文件
        with open(sql_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
            
        print(f"SQL脚本已生成: {sql_file}")
        return True
    except Exception as e:
        print(f"生成SQL脚本时出错: {str(e)}")
        return False

def generate_raw_sql_helper():
    """生成使用原始SQL插入记录的辅助函数"""
    try:
        helper_file = os.path.join(PROJECT_ROOT, 'app/utils/raw_sql_helper.py')
        
        # 创建目录
        os.makedirs(os.path.dirname(helper_file), exist_ok=True)
        
        # 辅助函数内容
        helper_content = """\"\"\"
使用原始SQL插入记录的辅助函数

此模块提供了使用原始SQL插入记录的辅助函数，避免SQLAlchemy的日期时间处理问题。
\"\"\"

from app import db
from sqlalchemy import text
from datetime import datetime
import json

def insert_area_change_history(area_id, change_type, old_parent_id=None, new_parent_id=None, 
                              old_data=None, new_data=None, changed_by=None):
    \"\"\"
    使用原始SQL插入area_change_history记录
    
    参数:
        area_id: 区域ID
        change_type: 变更类型，例如'create'、'update'、'delete'
        old_parent_id: 旧的父级ID
        new_parent_id: 新的父级ID
        old_data: 旧的数据，JSON字符串或字典
        new_data: 新的数据，JSON字符串或字典
        changed_by: 变更人ID
    
    返回:
        新插入记录的ID
    \"\"\"
    # 处理JSON数据
    if old_data and not isinstance(old_data, str):
        old_data = json.dumps(old_data)
    if new_data and not isinstance(new_data, str):
        new_data = json.dumps(new_data)
    
    # 构建SQL语句
    sql = text('''
    INSERT INTO area_change_history
    (area_id, change_type, old_parent_id, new_parent_id, old_data, new_data, changed_by, created_at)
    OUTPUT inserted.id
    VALUES
    (:area_id, :change_type, :old_parent_id, :new_parent_id, :old_data, :new_data, :changed_by, GETDATE())
    ''')
    
    # 执行SQL
    result = db.session.execute(
        sql,
        {
            'area_id': area_id,
            'change_type': change_type,
            'old_parent_id': old_parent_id,
            'new_parent_id': new_parent_id,
            'old_data': old_data,
            'new_data': new_data,
            'changed_by': changed_by
        }
    )
    
    # 获取新插入记录的ID
    new_id = result.scalar()
    
    return new_id

def insert_audit_log(action, resource_type, resource_id=None, user_id=None, 
                    area_id=None, details=None, ip_address=None):
    \"\"\"
    使用原始SQL插入audit_log记录
    
    参数:
        action: 操作类型，例如'create'、'update'、'delete'、'login'
        resource_type: 资源类型，例如'User'、'Area'、'Recipe'
        resource_id: 资源ID
        user_id: 用户ID
        area_id: 区域ID
        details: 详细信息，JSON字符串或字典
        ip_address: IP地址
    
    返回:
        新插入记录的ID
    \"\"\"
    # 处理JSON数据
    if details and not isinstance(details, str):
        details = json.dumps(details)
    
    # 构建SQL语句
    sql = text('''
    INSERT INTO audit_log
    (action, resource_type, resource_id, user_id, area_id, details, ip_address, created_at)
    OUTPUT inserted.id
    VALUES
    (:action, :resource_type, :resource_id, :user_id, :area_id, :details, :ip_address, GETDATE())
    ''')
    
    # 执行SQL
    result = db.session.execute(
        sql,
        {
            'action': action,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'user_id': user_id,
            'area_id': area_id,
            'details': details,
            'ip_address': ip_address
        }
    )
    
    # 获取新插入记录的ID
    new_id = result.scalar()
    
    return new_id
"""
        
        # 写入辅助函数文件
        with open(helper_file, 'w', encoding='utf-8') as f:
            f.write(helper_content)
            
        print(f"辅助函数已生成: {helper_file}")
        return True
    except Exception as e:
        print(f"生成辅助函数时出错: {str(e)}")
        return False

def fix_area_routes():
    """修复area/routes.py文件，使用原始SQL插入AreaChangeHistory记录"""
    try:
        file_path = os.path.join(PROJECT_ROOT, 'app/area/routes.py')
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False

        # 备份文件
        if not backup_file(file_path):
            print(f"备份文件失败，跳过修复: {file_path}")
            return False

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 添加导入语句
        if 'from app.utils.raw_sql_helper import insert_area_change_history' not in content:
            import_pattern = re.compile(r'(from\s+.*?\s+import\s+.*?\n)')
            matches = list(import_pattern.finditer(content))
            if matches:
                last_import = matches[-1]
                content = content[:last_import.end()] + 'from app.utils.raw_sql_helper import insert_area_change_history\n' + content[last_import.end():]

        # 修复1: 添加区域时的AreaChangeHistory记录
        pattern1 = r"""        # 记录变更历史
        history = AreaChangeHistory\(
            area_id=area\.id,
            change_type='create',
            new_parent_id=area\.parent_id,
            new_data=json\.dumps\(area\.to_dict\(\)\),
            changed_by=current_user\.id
        \)
        db\.session\.add\(history\)"""
        
        replacement1 = """        # 记录变更历史 - 使用原始SQL避免日期时间精度问题
        insert_area_change_history(
            area_id=area.id,
            change_type='create',
            new_parent_id=area.parent_id,
            new_data=area.to_dict(),
            changed_by=current_user.id
        )"""
        
        content = content.replace(pattern1, replacement1)

        # 修复2: 编辑区域时的AreaChangeHistory记录
        pattern2 = r"""        # 记录变更历史
        history = AreaChangeHistory\(
            area_id=area\.id,
            change_type='update',
            old_parent_id=old_parent_id,
            new_parent_id=area\.parent_id,
            old_data=json\.dumps\(old_data\),
            new_data=json\.dumps\(area\.to_dict\(\)\),
            changed_by=current_user\.id
        \)
        db\.session\.add\(history\)"""
        
        replacement2 = """        # 记录变更历史 - 使用原始SQL避免日期时间精度问题
        insert_area_change_history(
            area_id=area.id,
            change_type='update',
            old_parent_id=old_parent_id,
            new_parent_id=area.parent_id,
            old_data=old_data,
            new_data=area.to_dict(),
            changed_by=current_user.id
        )"""
        
        content = content.replace(pattern2, replacement2)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改文件: {file_path}")
            return True
        else:
            print(f"文件无需修改: {file_path}")
            return False

    except Exception as e:
        print(f"修改文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始全面修复datetime精度问题...")

    # 步骤1: 修改所有模型文件中的DateTime字段定义
    print("\n步骤1: 修改所有模型文件中的DateTime字段定义")
    modified_count = 0
    for model_file in MODEL_FILES:
        if os.path.exists(os.path.join(PROJECT_ROOT, model_file)):
            modified_count += fix_datetime_in_model_file(model_file)
    print(f"共修改了 {modified_count} 个模型文件")

    # 步骤2: 生成SQL脚本来修改数据库中的datetime2字段精度
    print("\n步骤2: 生成SQL脚本来修改数据库中的datetime2字段精度")
    if generate_sql_script():
        print("SQL脚本生成成功，请在SQL Server Management Studio中执行该脚本")
    else:
        print("SQL脚本生成失败")

    # 步骤3: 生成使用原始SQL插入记录的辅助函数
    print("\n步骤3: 生成使用原始SQL插入记录的辅助函数")
    if generate_raw_sql_helper():
        print("辅助函数生成成功")
    else:
        print("辅助函数生成失败")

    # 步骤4: 修复area/routes.py文件，使用原始SQL插入AreaChangeHistory记录
    print("\n步骤4: 修复area/routes.py文件，使用原始SQL插入AreaChangeHistory记录")
    if fix_area_routes():
        print("area/routes.py文件修复成功")
    else:
        print("area/routes.py文件修复失败或无需修复")

    print("\n全面修复datetime精度问题完成")
    print("请重启应用程序以应用更改")

if __name__ == "__main__":
    main()
