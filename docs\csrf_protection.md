# CSRF保护指南

## 概述

本项目使用Flask-WTF提供的CSRF保护机制，确保所有表单和AJAX请求都受到CSRF攻击的保护。

## 配置

CSRF保护已在`app/__init__.py`中配置：

```python
from flask_wtf.csrf import CSRFProtect
csrf = CSRFProtect()

def create_app(config_name=None):
    # ...
    csrf.init_app(app)
    # ...
```

## 在表单中使用CSRF保护

### 使用WTForms表单

所有表单类都应继承自`FlaskForm`，这样会自动包含CSRF保护：

```python
from flask_wtf import FlaskForm
from wtforms import StringField, SubmitField

class MyForm(FlaskForm):
    name = StringField('名称')
    submit = SubmitField('提交')
```

在模板中使用表单时，使用`form.hidden_tag()`或`form.csrf_token`包含CSRF令牌：

```html
<form method="post">
    {{ form.hidden_tag() }}
    <!-- 或者 -->
    {{ form.csrf_token }}
    
    <!-- 其他表单字段 -->
    {{ form.name.label }} {{ form.name() }}
    {{ form.submit() }}
</form>
```

### 使用原生HTML表单

如果不使用WTForms，可以直接在表单中包含CSRF令牌：

```html
<form method="post">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <!-- 其他表单字段 -->
</form>
```

## 在AJAX请求中使用CSRF保护

### 在base.html中包含CSRF令牌

确保在`base.html`的`<head>`部分包含CSRF令牌的meta标签：

```html
<meta name="csrf-token" content="{{ csrf_token() }}">
```

### 在JavaScript中设置AJAX请求头

在JavaScript中，使用以下代码为所有AJAX请求添加CSRF令牌：

```javascript
const csrfToken = $('meta[name=csrf-token]').attr('content');
if (csrfToken) {
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrfToken);
            }
        }
    });
}
```

这段代码已包含在`app/static/js/auth-helper.js`中，会在页面加载时自动执行。

### 单个AJAX请求

如果需要为单个AJAX请求添加CSRF令牌，可以这样做：

```javascript
$.ajax({
    url: '/api/endpoint',
    type: 'POST',
    data: data,
    headers: {
        'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
    },
    success: function(response) {
        // 处理响应
    }
});
```

## 常见问题

### CSRF验证失败

如果遇到CSRF验证失败的错误，可能的原因包括：

1. 表单中没有包含CSRF令牌
2. 会话已过期
3. AJAX请求没有包含CSRF令牌头部

### 解决方法

1. 确保所有表单都包含`form.hidden_tag()`或`form.csrf_token`
2. 确保所有原生HTML表单都包含`<input type="hidden" name="csrf_token" value="{{ csrf_token() }}">`
3. 确保所有AJAX请求都包含CSRF令牌头部
4. 检查会话是否过期，可能需要重新登录

## 最佳实践

1. 始终使用`FlaskForm`作为表单的基类
2. 在模板中使用`form.hidden_tag()`包含CSRF令牌
3. 使用`auth-helper.js`中的AJAX设置来处理CSRF令牌
4. 不要创建自定义的CSRF保护机制，使用Flask-WTF提供的标准机制
