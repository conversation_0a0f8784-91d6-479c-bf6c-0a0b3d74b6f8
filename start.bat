@echo off
echo 正在启动系统...

:: 检查虚拟环境是否存在，不存在则自动创建
if not exist "venv\Scripts\activate.bat" (
    echo 虚拟环境不存在，正在自动创建...
    python -m venv venv
    call venv\Scripts\activate.bat
    python -m pip install --upgrade pip
) else (
    :: 激活虚拟环境
    call venv\Scripts\activate.bat
)

:: 检查依赖是否安装
if not exist "requirements.txt" (
    echo 错误：requirements.txt 文件不存在！
    exit /b 1
)

:: 安装依赖
echo 正在检查并安装依赖...
pip install -r requirements.txt

:: 启动应用
echo 正在启动应用...
python run.py

:: 如果应用异常退出，自动重启
if errorlevel 1 (
    echo 应用异常退出，正在重新启动...
    timeout /t 5 /nobreak
    goto :start
)

:: 退出虚拟环境
deactivate