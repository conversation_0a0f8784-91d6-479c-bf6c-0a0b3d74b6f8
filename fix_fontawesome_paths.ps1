$cssFile = "app/static/vendor/fontawesome/css/all.min.css"
$content = Get-Content -Path $cssFile -Raw

# 替换字体路径
$content = $content -replace 'url\(\.\.\/webfonts\/', 'url(../webfonts/'
$content = $content -replace 'url\(\.\/webfonts\/', 'url(../webfonts/'
$content = $content -replace 'url\(/webfonts\/', 'url(../webfonts/'

# 保存修改后的内容
Set-Content -Path $cssFile -Value $content

Write-Host "Font Awesome CSS 文件中的字体路径已修复！"
