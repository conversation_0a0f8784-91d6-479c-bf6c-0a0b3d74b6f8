#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库连接测试工具
用于测试SQL Server数据库连接配置
"""

import os
import sys
from urllib.parse import quote_plus
import pyodbc
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

def test_pyodbc_connection():
    """使用pyodbc直接测试数据库连接"""
    print("=" * 60)
    print("测试 pyodbc 直接连接...")

    # 获取配置
    server = os.environ.get('DB_SERVER', 'localhost\\SQLEXPRESS')
    database = os.environ.get('DB_DATABASE', 'StudentsCMSSP')
    username = os.environ.get('DB_USERNAME')
    password = os.environ.get('DB_PASSWORD')
    driver = os.environ.get('DB_DRIVER', 'SQL Server')

    print(f"服务器: {server}")
    print(f"数据库: {database}")
    print(f"驱动: {driver}")
    print(f"认证方式: {'SQL Server认证' if username else 'Windows认证'}")

    try:
        # 构建连接字符串
        if username and password:
            conn_str = f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
        else:
            conn_str = f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};Trusted_Connection=yes"

        print(f"连接字符串: {conn_str.replace(password or '', '***' if password else '')}")

        # 尝试连接
        conn = pyodbc.connect(conn_str, timeout=10)
        cursor = conn.cursor()

        # 执行测试查询
        cursor.execute("SELECT @@VERSION as version, DB_NAME() as current_db, GETDATE() as current_time")
        row = cursor.fetchone()

        print("✅ pyodbc 连接成功!")
        version_info = row.version.split('\n')[0]
        print(f"SQL Server版本: {version_info}")
        print(f"当前数据库: {row.current_db}")
        print(f"服务器时间: {row.current_time}")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ pyodbc 连接失败: {str(e)}")
        return False

def test_sqlalchemy_connection():
    """使用SQLAlchemy测试数据库连接"""
    print("=" * 60)
    print("测试 SQLAlchemy 连接...")

    # 获取配置
    server = os.environ.get('DB_SERVER', 'localhost\\SQLEXPRESS')
    database = os.environ.get('DB_DATABASE', 'StudentsCMSSP')
    username = os.environ.get('DB_USERNAME')
    password = os.environ.get('DB_PASSWORD')
    driver = os.environ.get('DB_DRIVER', 'SQL Server')

    try:
        # 构建连接字符串
        if username and password:
            conn_str = f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
        else:
            conn_str = f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};Trusted_Connection=yes"

        quoted_conn_str = quote_plus(conn_str)
        database_url = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

        print(f"SQLAlchemy URL: {database_url.replace(quote_plus(password) if password else '', '***' if password else '')}")

        # 创建引擎
        engine = create_engine(
            database_url,
            pool_recycle=280,
            pool_timeout=20,
            pool_size=5,
            max_overflow=10,
            echo=False
        )

        # 测试连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT @@VERSION as version, DB_NAME() as current_db, GETDATE() as current_time"))
            row = result.fetchone()

            print("✅ SQLAlchemy 连接成功!")
            version_info = row.version.split('\n')[0]
            print(f"SQL Server版本: {version_info}")
            print(f"当前数据库: {row.current_db}")
            print(f"服务器时间: {row.current_time}")

        return True

    except SQLAlchemyError as e:
        print(f"❌ SQLAlchemy 连接失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def list_available_drivers():
    """列出可用的ODBC驱动"""
    print("=" * 60)
    print("可用的 ODBC 驱动:")

    try:
        drivers = pyodbc.drivers()
        for i, driver in enumerate(drivers, 1):
            print(f"{i}. {driver}")

        # 推荐的驱动
        recommended = []
        for driver in drivers:
            if 'SQL Server' in driver:
                recommended.append(driver)

        if recommended:
            print("\n推荐的SQL Server驱动:")
            for driver in recommended:
                print(f"  - {driver}")

    except Exception as e:
        print(f"❌ 无法获取驱动列表: {str(e)}")

def main():
    """主函数"""
    print("SQL Server 数据库连接测试工具")
    print("=" * 60)

    # 显示当前配置
    print("当前环境变量配置:")
    default_server = '未设置 (默认: localhost\\SQLEXPRESS)'
    print(f"DB_SERVER: {os.environ.get('DB_SERVER', default_server)}")
    print(f"DB_DATABASE: {os.environ.get('DB_DATABASE', '未设置 (默认: StudentsCMSSP)')}")
    print(f"DB_USERNAME: {os.environ.get('DB_USERNAME', '未设置 (将使用Windows认证)')}")
    print(f"DB_PASSWORD: {'已设置' if os.environ.get('DB_PASSWORD') else '未设置'}")
    print(f"DB_DRIVER: {os.environ.get('DB_DRIVER', '未设置 (默认: SQL Server)')}")
    print()

    # 列出可用驱动
    list_available_drivers()

    # 测试连接
    pyodbc_success = test_pyodbc_connection()
    sqlalchemy_success = test_sqlalchemy_connection()

    print("=" * 60)
    print("测试结果总结:")
    print(f"pyodbc 连接: {'✅ 成功' if pyodbc_success else '❌ 失败'}")
    print(f"SQLAlchemy 连接: {'✅ 成功' if sqlalchemy_success else '❌ 失败'}")

    if pyodbc_success and sqlalchemy_success:
        print("\n🎉 所有测试通过！数据库配置正确。")
        return 0
    else:
        print("\n⚠️  部分或全部测试失败，请检查数据库配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
