Write-Host "正在启动系统..." -ForegroundColor Green

# 检查虚拟环境是否存在，不存在则自动创建
if (-not (Test-Path "venv\Scripts\Activate.ps1")) {
    Write-Host "虚拟环境不存在，正在自动创建..." -ForegroundColor Yellow
    python -m venv
    & "venv\Scripts\Activate.ps1"
    python -m pip install --upgrade pip
} else {
    # 激活虚拟环境
    & "venv\Scripts\Activate.ps1"
}

# 检查依赖是否安装
if (-not (Test-Path "requirements.txt")) {
    Write-Host "错误：requirements.txt 文件不存在！" -ForegroundColor Red
    exit 1
}

# 安装依赖
Write-Host "正在检查并安装依赖..." -ForegroundColor Yellow
pip install -r requirements.txt

# 启动应用
Write-Host "正在启动应用..." -ForegroundColor Green
while ($true) {
    try {
        python run.py
    }
    catch {
        Write-Host "应用异常退出，5秒后重新启动..." -ForegroundColor Red
        Start-Sleep -Seconds 5
        continue
    }
    break
}

# 退出虚拟环境
deactivate