# SSL/HTTPS Configuration Guide

This document explains how to configure SSL/HTTPS for your Flask application to resolve SSL handshake errors.

## Problem Description

The SSL handshake errors you're seeing occur when:
- Clients try to access your HTTP server using HTTPS
- Load balancers or reverse proxies expect HTTPS
- Browser security policies force HTTPS connections

## Solutions

### Option 1: Enable HTTPS (Recommended)

#### Using Environment Variables
Set the following environment variables:
```bash
HOST=0.0.0.0
PORT=5000
SSL_ENABLED=true
SSL_CONTEXT=adhoc
```

#### Using .env File
1. Copy `.env.example` to `.env`
2. Set `HOST=0.0.0.0` and `PORT=5000`
3. Set `SSL_ENABLED=true`
4. Set `SSL_CONTEXT=adhoc` for auto-generated certificates

#### Using Custom Certificates
1. Obtain SSL certificates (cert.pem and key.pem)
2. Set `SSL_CONTEXT=cert.pem,key.pem`

### Option 2: Keep HTTP Only

If you want to keep HTTP only, ensure clients access via:
- `http://your-server:5000` (not https://)
- Configure any reverse proxies to use HTTP backend

## Configuration Options

### HOST
- Default: `0.0.0.0` (listen on all interfaces)
- Set to `127.0.0.1` for localhost only

### PORT
- Default: `5000`
- Can be changed via environment variable

### SSL_ENABLED
- `true`: Enable HTTPS
- `false`: Use HTTP only (default)

### SSL_CONTEXT
- `adhoc`: Auto-generated self-signed certificate (development only)
- `cert.pem,key.pem`: Custom certificate files
- Path to certificate bundle

## Development vs Production

### Development
- Use `SSL_CONTEXT=adhoc` for quick HTTPS setup
- Browsers will show security warnings (normal for self-signed certs)
- Access via: `https://localhost:5000`

### Production
- Use proper SSL certificates from a trusted CA
- Configure reverse proxy (nginx/Apache) for SSL termination
- Set `SSL_CONTEXT=cert.pem,key.pem`

## Installation Requirements

For HTTPS support, install the cryptography package:
```bash
pip install cryptography
```

## Testing

### HTTP Mode
```bash
curl http://localhost:5000/api/check-login-status
```

### HTTPS Mode
```bash
curl -k https://localhost:5000/api/check-login-status
```
(The `-k` flag ignores certificate warnings for self-signed certs)

## Troubleshooting

### SSL Handshake Errors
- Check if clients are using correct protocol (http vs https)
- Verify SSL configuration in environment variables
- Check server logs for SSL context information

### Port Configuration
- Ensure PORT environment variable is set correctly
- Check if port 5000 is available and not blocked by firewall
- Verify HOST setting allows connections from your client

### Certificate Errors
- Ensure certificate files exist and are readable
- Verify certificate format (PEM)
- Check certificate expiration

### Browser Warnings
- Normal for self-signed certificates in development
- Click "Advanced" and "Proceed" to continue
- Use proper certificates in production

## Security Notes

1. **Never use self-signed certificates in production**
2. **Always use HTTPS in production environments**
3. **Keep private keys secure and never commit them to version control**
4. **Regularly update SSL certificates before expiration**

## Example Configurations

### Development with HTTPS
```bash
HOST=0.0.0.0
PORT=5000
SSL_ENABLED=true
SSL_CONTEXT=adhoc
```

### Production with Custom Certificates
```bash
HOST=0.0.0.0
PORT=5000
SSL_ENABLED=true
SSL_CONTEXT=/path/to/cert.pem,/path/to/key.pem
```

### HTTP Only (Default)
```bash
HOST=0.0.0.0
PORT=5000
SSL_ENABLED=false
```

## Quick Start

1. **Set port to 5000** (already configured by default)
2. **Choose HTTP or HTTPS mode**
3. **Restart your application**

Your application will now run on port 5000 with proper SSL error handling.
