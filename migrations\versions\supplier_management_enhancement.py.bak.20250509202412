"""增强供应商管理模块

Revision ID: supplier_management_enhancement
Revises: ec19dce5184b
Create Date: 2023-07-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'supplier_management_enhancement'
down_revision = 'ec19dce5184b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # 添加供应商-学校关联表
    op.create_table('supplier_school_relations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('supplier_id', sa.Integer(), nullable=False),
    sa.Column('area_id', sa.Integer(), nullable=False),
    sa.Column('contract_number', sa.String(length=100), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False, server_default=sa.text('1')),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.ForeignKeyConstraint(['area_id'], ['administrative_areas.id'], ),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    # 添加产品规格参数表
    op.create_table('product_spec_parameters',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('param_name', sa.String(length=50), nullable=False),
    sa.Column('param_value', sa.String(length=100), nullable=False),
    sa.Column('param_unit', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.ForeignKeyConstraint(['product_id'], ['supplier_products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    # 添加送货验收表
    op.create_table('delivery_inspections',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('delivery_id', sa.Integer(), nullable=False),
    sa.Column('inspector_id', sa.Integer(), nullable=False),
    sa.Column('inspection_date', sa.DateTime(), nullable=False),
    sa.Column('inspection_result', sa.String(length=20), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.ForeignKeyConstraint(['delivery_id'], ['supplier_deliveries.id'], ),
    sa.ForeignKeyConstraint(['inspector_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    # 添加送货项验收表
    op.create_table('delivery_item_inspections',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('inspection_id', sa.Integer(), nullable=False),
    sa.Column('delivery_item_id', sa.Integer(), nullable=False),
    sa.Column('accepted_quantity', sa.Float(), nullable=False),
    sa.Column('rejected_quantity', sa.Float(), nullable=False),
    sa.Column('rejection_reason', sa.String(length=200), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
    sa.ForeignKeyConstraint(['delivery_item_id'], ['delivery_items.id'], ),
    sa.ForeignKeyConstraint(['inspection_id'], ['delivery_inspections.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    # 修改供应商产品表，添加新字段
    with op.batch_alter_table('supplier_products', schema=None) as batch_op:
        batch_op.add_column(sa.Column('product_code', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('product_name', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('shelf_status', sa.Integer(), nullable=False, server_default=sa.text('0')))
        batch_op.add_column(sa.Column('shelf_time', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('shelf_operator_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('description', sa.Text(), nullable=True))
        batch_op.create_foreign_key('fk_supplier_products_shelf_operator', 'users', ['shelf_operator_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # 删除外键约束
    with op.batch_alter_table('supplier_products', schema=None) as batch_op:
        batch_op.drop_constraint('fk_supplier_products_shelf_operator', type_='foreignkey')
        batch_op.drop_column('description')
        batch_op.drop_column('shelf_operator_id')
        batch_op.drop_column('shelf_time')
        batch_op.drop_column('shelf_status')
        batch_op.drop_column('product_name')
        batch_op.drop_column('product_code')

    # 删除表
    op.drop_table('delivery_item_inspections')
    op.drop_table('delivery_inspections')
    op.drop_table('product_spec_parameters')
    op.drop_table('supplier_school_relations')

    # ### end Alembic commands ###
