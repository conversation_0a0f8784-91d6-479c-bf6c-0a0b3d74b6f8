"""
综合修复食堂日常管理、库存管理和员工管理模块的 DATETIME2 字段精度问题

此脚本使用系统自带的框架修复数据库中相关模块的 DATETIME2 字段精度问题。
涉及的模块包括：
1. 食堂日常管理模块
2. 库存管理模块
3. 员工管理模块
"""
from app import db, create_app
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_daily_management_datetime_precision():
    """修复食堂日常管理模块相关表的 DATETIME2 字段精度问题"""
    try:
        # 修复 daily_logs 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'daily_logs'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            -- 先删除默认约束
            DECLARE @ConstraintName nvarchar(200)

            -- 查找created_at列上的默认约束
            SELECT @ConstraintName = dc.name
            FROM sys.default_constraints dc
            JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
            WHERE OBJECT_NAME(dc.parent_object_id) = 'daily_logs' AND c.name = 'created_at'

            IF @ConstraintName IS NOT NULL
            BEGIN
                EXEC('ALTER TABLE daily_logs DROP CONSTRAINT ' + @ConstraintName)
                PRINT 'daily_logs 表的 created_at 默认约束已删除';
            END

            -- 查找updated_at列上的默认约束
            SELECT @ConstraintName = dc.name
            FROM sys.default_constraints dc
            JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
            WHERE OBJECT_NAME(dc.parent_object_id) = 'daily_logs' AND c.name = 'updated_at'

            IF @ConstraintName IS NOT NULL
            BEGIN
                EXEC('ALTER TABLE daily_logs DROP CONSTRAINT ' + @ConstraintName)
                PRINT 'daily_logs 表的 updated_at 默认约束已删除';
            END

            -- 修改列类型
            ALTER TABLE daily_logs
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            ALTER TABLE daily_logs
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;

            -- 重新添加默认约束
            ALTER TABLE daily_logs
            ADD CONSTRAINT DF_daily_logs_created_at DEFAULT (GETDATE()) FOR created_at;

            ALTER TABLE daily_logs
            ADD CONSTRAINT DF_daily_logs_updated_at DEFAULT (GETDATE()) FOR updated_at;

            PRINT 'daily_logs 表的 created_at 和 updated_at 字段已修复';
        END
        """))

        # 修复 inspection_records/daily_inspections 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'inspection_records'
            AND COLUMN_NAME = 'inspection_time'
        )
        BEGIN
            ALTER TABLE inspection_records
            ALTER COLUMN inspection_time DATETIME2(1) NOT NULL;

            IF EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'inspection_records'
                AND COLUMN_NAME = 'created_at'
            )
            BEGIN
                ALTER TABLE inspection_records
                ALTER COLUMN created_at DATETIME2(1) NOT NULL;

                PRINT 'inspection_records 表的 created_at 字段已修复';
            END

            PRINT 'inspection_records 表的 inspection_time 字段已修复';
        END
        """))

        # 检查并修复 daily_inspections 表（如果存在）
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'daily_inspections'
            AND COLUMN_NAME = 'inspection_time'
        )
        BEGIN
            ALTER TABLE daily_inspections
            ALTER COLUMN inspection_time DATETIME2(1) NOT NULL;

            PRINT 'daily_inspections 表的 inspection_time 字段已修复';
        END
        """))

        # 修复 dining_companions 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'dining_companions'
            AND COLUMN_NAME = 'dining_time'
        )
        BEGIN
            ALTER TABLE dining_companions
            ALTER COLUMN dining_time DATETIME2(1) NOT NULL;

            IF EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'dining_companions'
                AND COLUMN_NAME = 'created_at'
            )
            BEGIN
                ALTER TABLE dining_companions
                ALTER COLUMN created_at DATETIME2(1) NOT NULL;

                PRINT 'dining_companions 表的 created_at 字段已修复';
            END

            PRINT 'dining_companions 表的 dining_time 字段已修复';
        END
        """))

        # 修复 canteen_training_records 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'canteen_training_records'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE canteen_training_records
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'canteen_training_records 表的 created_at 字段已修复';
        END
        """))

        # 修复 special_events 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'special_events'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE special_events
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'special_events 表的 created_at 字段已修复';
        END
        """))

        # 修复 issues 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'issues'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE issues
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'issues 表的 created_at 字段已修复';
        END
        """))

        logger.info("食堂日常管理模块所有 DATETIME2 字段的精度问题已修复")
        return True
    except Exception as e:
        logger.error(f"修复食堂日常管理模块 DATETIME2 字段精度时出错: {str(e)}")
        return False

def fix_inventory_datetime_precision():
    """修复库存管理模块相关表的 DATETIME2 字段精度问题"""
    try:
        # 修复 warehouses 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'warehouses'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE warehouses
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'warehouses 表的 created_at 字段已修复';
        END
        """))

        # 修复 inventories 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'inventories'
            AND COLUMN_NAME = 'updated_at'
        )
        BEGIN
            ALTER TABLE inventories
            ALTER COLUMN updated_at DATETIME2(1) NULL;

            PRINT 'inventories 表的 updated_at 字段已修复';
        END
        """))

        # 修复 inventory_alerts 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'inventory_alerts'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE inventory_alerts
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'inventory_alerts 表的 created_at 字段已修复';
        END
        """))

        # 修复 stock_ins 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'stock_ins'
            AND COLUMN_NAME = 'stock_in_date'
        )
        BEGIN
            ALTER TABLE stock_ins
            ALTER COLUMN stock_in_date DATETIME2(1) NOT NULL;

            ALTER TABLE stock_ins
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            ALTER TABLE stock_ins
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;

            PRINT 'stock_ins 表的 stock_in_date, created_at 和 updated_at 字段已修复';
        END
        """))

        # 修复 stock_in_items 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'stock_in_items'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE stock_in_items
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'stock_in_items 表的 created_at 字段已修复';
        END
        """))

        # 修复 stock_outs 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'stock_outs'
            AND COLUMN_NAME = 'stock_out_date'
        )
        BEGIN
            ALTER TABLE stock_outs
            ALTER COLUMN stock_out_date DATETIME2(1) NOT NULL;

            ALTER TABLE stock_outs
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            ALTER TABLE stock_outs
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;

            PRINT 'stock_outs 表的 stock_out_date, created_at 和 updated_at 字段已修复';
        END
        """))

        # 修复 stock_out_items 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'stock_out_items'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE stock_out_items
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'stock_out_items 表的 created_at 字段已修复';
        END
        """))

        # 修复 inventory_checks 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'inventory_checks'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE inventory_checks
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            IF EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'inventory_checks'
                AND COLUMN_NAME = 'updated_at'
            )
            BEGIN
                ALTER TABLE inventory_checks
                ALTER COLUMN updated_at DATETIME2(1) NOT NULL;

                PRINT 'inventory_checks 表的 updated_at 字段已修复';
            END

            PRINT 'inventory_checks 表的 created_at 字段已修复';
        END
        """))

        logger.info("库存管理模块所有 DATETIME2 字段的精度问题已修复")
        return True
    except Exception as e:
        logger.error(f"修复库存管理模块 DATETIME2 字段精度时出错: {str(e)}")
        return False

def fix_employee_datetime_precision():
    """修复员工管理模块相关表的 DATETIME2 字段精度问题"""
    try:
        # 修复 employees 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'employees'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE employees
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            ALTER TABLE employees
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;

            PRINT 'employees 表的 created_at 和 updated_at 字段已修复';
        END
        """))

        # 修复 health_certificates 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'health_certificates'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE health_certificates
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            IF EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = 'health_certificates'
                AND COLUMN_NAME = 'updated_at'
            )
            BEGIN
                ALTER TABLE health_certificates
                ALTER COLUMN updated_at DATETIME2(1) NOT NULL;

                PRINT 'health_certificates 表的 updated_at 字段已修复';
            END

            PRINT 'health_certificates 表的 created_at 字段已修复';
        END
        """))

        # 修复 medical_examinations 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'medical_examinations'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE medical_examinations
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'medical_examinations 表的 created_at 字段已修复';
        END
        """))

        # 修复 daily_health_checks 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'daily_health_checks'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE daily_health_checks
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'daily_health_checks 表的 created_at 字段已修复';
        END
        """))

        # 修复 training_records 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'training_records'
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE training_records
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;

            PRINT 'training_records 表的 created_at 字段已修复';
        END
        """))

        logger.info("员工管理模块所有 DATETIME2 字段的精度问题已修复")
        return True
    except Exception as e:
        logger.error(f"修复员工管理模块 DATETIME2 字段精度时出错: {str(e)}")
        return False

def fix_comprehensive_datetime_precision():
    """综合修复所有模块的 DATETIME2 字段精度问题"""
    try:
        # 开始事务
        success_daily = fix_daily_management_datetime_precision()
        success_inventory = fix_inventory_datetime_precision()
        success_employee = fix_employee_datetime_precision()

        # 提交事务
        db.session.commit()

        if success_daily and success_inventory and success_employee:
            logger.info("所有模块的 DATETIME2 字段精度问题已成功修复")
            return True
        else:
            logger.warning("部分模块的 DATETIME2 字段精度修复失败，请查看日志")
            return False
    except Exception as e:
        db.session.rollback()
        logger.error(f"修复 DATETIME2 字段精度时出错: {str(e)}")
        return False

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        if fix_comprehensive_datetime_precision():
            print("成功修复所有模块的 DATETIME2 字段精度问题")
        else:
            print("修复 DATETIME2 字段精度时出错，请查看日志")
