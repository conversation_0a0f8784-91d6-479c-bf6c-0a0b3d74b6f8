from app import create_app, db
from sqlalchemy import text

app = create_app()

with app.app_context():
    # 检查表是否存在
    try:
        result = db.session.execute(text("SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'photos'"))
        exists = result.fetchone() is not None
        
        if exists:
            print("photos 表已存在")
        else:
            # 创建表
            print("创建 photos 表...")
            create_table_sql = text("""
            CREATE TABLE photos (
                id INT IDENTITY(1,1) PRIMARY KEY,
                reference_id INT NOT NULL,
                reference_type NVARCHAR(20) NOT NULL,
                file_name NVARCHAR(255) NOT NULL,
                file_path NVARCHAR(255) NOT NULL,
                description NVARCHAR(255) NULL,
                rating INT DEFAULT 3,
                upload_time DATETIME2(1) DEFAULT GETDATE() NOT NULL
            );
            
            CREATE INDEX idx_photos_reference ON photos (reference_type, reference_id);
            """)
            
            db.session.execute(create_table_sql)
            db.session.commit()
            print("photos 表创建成功")
    except Exception as e:
        print(f"错误: {str(e)}")
