{% extends 'base_public.html' %}

{% block title %}上传{{ inspection_type_name }}照片{% endblock %}

{% block styles %}
<style>
    body {
        background-color: #f8f9fc;
    }
    
    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }
    
    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .school-name {
        font-size: 1.2rem;
    }
    
    .upload-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        background-color: white;
    }
    
    .upload-card .card-header {
        background-color: #4e73df;
        color: white;
        font-weight: bold;
        padding: 15px;
    }
    
    .upload-card .card-body {
        padding: 20px;
    }
    
    .photo-preview-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    
    .photo-preview {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 5px;
        border: 1px solid #e3e6f0;
    }
    
    .upload-btn {
        background-color: #4e73df;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        margin-top: 15px;
    }
    
    .upload-btn:hover {
        background-color: #2e59d9;
    }
    
    .success-message {
        display: none;
        background-color: #1cc88a;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }
    
    .error-message {
        display: none;
        background-color: #e74a3b;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        margin-top: 15px;
    }
    
    .footer {
        background-color: #f8f9fc;
        padding: 20px 0;
        margin-top: 30px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="/static/img/logo.png" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂监管平台</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="upload-card">
                <div class="card-header">
                    <i class="fas fa-camera mr-2"></i> 上传{{ inspection_type_name }}照片
                </div>
                <div class="card-body">
                    <p class="mb-4">
                        您正在为 <strong>{{ school.name }}</strong> 上传 <strong>{{ log.log_date.strftime('%Y-%m-%d') }}</strong> 的{{ inspection_type_name }}照片。
                        请选择检查项目并上传相关照片。
                    </p>
                    
                    <form id="uploadForm">
                        <div class="form-group">
                            <label for="inspectionItem"><strong>检查项目</strong></label>
                            <select class="form-control" id="inspectionItem" required>
                                <option value="">-- 请选择检查项目 --</option>
                                {% for item in inspection_items %}
                                <option value="{{ item }}">{{ item }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="description"><strong>描述</strong> (可选)</label>
                            <textarea class="form-control" id="description" rows="2" placeholder="请输入照片描述..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="photos"><strong>照片</strong> (可选择多张)</label>
                            <input type="file" class="form-control-file" id="photos" accept="image/*" multiple>
                            <small class="form-text text-muted">支持JPG、PNG格式，每张照片不超过5MB</small>
                        </div>
                        
                        <div class="photo-preview-container" id="photoPreviewContainer"></div>
                        
                        <div class="loading-spinner" id="loadingSpinner">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">正在上传照片，请稍候...</p>
                        </div>
                        
                        <div class="success-message" id="successMessage">
                            <i class="fas fa-check-circle mr-2"></i> 照片上传成功！
                        </div>
                        
                        <div class="error-message" id="errorMessage"></div>
                        
                        <button type="submit" class="btn upload-btn btn-block">
                            <i class="fas fa-upload mr-2"></i> 上传照片
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>© {{ now.year }} 校园餐智慧食堂监管平台 - 版权所有</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 照片预览
    document.getElementById('photos').addEventListener('change', function(e) {
        const container = document.getElementById('photoPreviewContainer');
        container.innerHTML = '';
        
        if (this.files.length > 0) {
            for (let i = 0; i < this.files.length; i++) {
                const file = this.files[i];
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'photo-preview';
                    container.appendChild(img);
                }
                
                reader.readAsDataURL(file);
            }
        }
    });
    
    // 表单提交
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const inspectionItem = document.getElementById('inspectionItem').value;
        const description = document.getElementById('description').value;
        const photos = document.getElementById('photos').files;
        
        if (!inspectionItem) {
            document.getElementById('errorMessage').textContent = '请选择检查项目';
            document.getElementById('errorMessage').style.display = 'block';
            return;
        }
        
        if (photos.length === 0) {
            document.getElementById('errorMessage').textContent = '请选择至少一张照片';
            document.getElementById('errorMessage').style.display = 'block';
            return;
        }
        
        // 隐藏错误信息，显示加载动画
        document.getElementById('errorMessage').style.display = 'none';
        document.getElementById('loadingSpinner').style.display = 'block';
        
        // 创建FormData对象
        const formData = new FormData();
        formData.append('inspection_item', inspectionItem);
        formData.append('description', description);
        
        // 添加所有照片
        for (let i = 0; i < photos.length; i++) {
            formData.append('photos', photos[i]);
        }
        
        // 发送请求
        fetch('/api/v2/inspections/public/upload-photos/{{ school.id }}/{{ log.id }}/{{ inspection_type }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingSpinner').style.display = 'none';
            
            if (data.success) {
                document.getElementById('successMessage').style.display = 'block';
                document.getElementById('uploadForm').reset();
                document.getElementById('photoPreviewContainer').innerHTML = '';
                
                // 3秒后隐藏成功信息
                setTimeout(() => {
                    document.getElementById('successMessage').style.display = 'none';
                }, 3000);
            } else {
                document.getElementById('errorMessage').textContent = data.error || '上传失败，请重试';
                document.getElementById('errorMessage').style.display = 'block';
            }
        })
        .catch(error => {
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('errorMessage').textContent = '上传失败，请重试';
            document.getElementById('errorMessage').style.display = 'block';
            console.error('Error:', error);
        });
    });
</script>
{% endblock %}
