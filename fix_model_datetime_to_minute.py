"""
修改模型定义中的时间字段，使其只精确到分钟级别

此脚本将修改所有模型文件中的DateTime字段定义，
添加一个默认值函数，使其在创建时就只精确到分钟级别。

注意：SQL Server中DATETIME2类型的最小精度为1，这确保了时间只精确到分钟。
"""

import os
import re
import sys

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 模型文件列表
MODEL_FILES = [
    "app/models.py",
    "app/models_phase1.py",
    "app/models_phase2.py",
    "app/models_phase3.py",
    "app/models_phase4.py",
    "app/models_recipe_advanced.py",
    "app/models_ingredient_traceability.py",
    "app/models_supplier.py",
    "app/models/notification.py"
]

def fix_datetime_in_file(file_path):
    """修改文件中的DateTime字段定义"""
    full_path = os.path.join(PROJECT_ROOT, file_path)

    # 检查文件是否存在
    if not os.path.exists(full_path):
        print(f"文件不存在: {full_path}")
        return 0

    try:
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 检查是否已导入datetime模块
        if 'from datetime import datetime' not in content and 'import datetime' not in content:
            # 添加导入语句
            content = 'from datetime import datetime\n' + content

        # 模式1: db.Column(db.DateTime, default=datetime.now, ...)
        pattern1 = r'db\.Column\(db\.DateTime,\s*default=datetime\.now,\s*(.+?)\)'
        replacement1 = r'db.Column(db.DateTime, default=lambda: datetime.now().replace(microsecond=0), \1)'
        content = re.sub(pattern1, replacement1, content)

        # 模式2: db.Column(db.DateTime, default=datetime.now)
        pattern2 = r'db\.Column\(db\.DateTime,\s*default=datetime\.now\)'
        replacement2 = r'db.Column(db.DateTime, default=lambda: datetime.now().replace(microsecond=0))'
        content = re.sub(pattern2, replacement2, content)

        # 模式3: db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, ...)
        pattern3 = r'db\.Column\(db\.DateTime,\s*default=datetime\.now,\s*onupdate=datetime\.now,\s*(.+?)\)'
        replacement3 = r'db.Column(db.DateTime, default=lambda: datetime.now().replace(microsecond=0), onupdate=lambda: datetime.now().replace(microsecond=0), \1)'
        content = re.sub(pattern3, replacement3, content)

        # 模式4: db.Column(db.DateTime, nullable=...)
        pattern4 = r'db\.Column\(db\.DateTime,\s*nullable=(.+?)\)'
        replacement4 = r'db.Column(db.DateTime, nullable=\1)'
        content = re.sub(pattern4, replacement4, content)

        # 模式5: db.Column(db.DateTime)
        pattern5 = r'db\.Column\(db\.DateTime\)'
        replacement5 = r'db.Column(db.DateTime)'
        content = re.sub(pattern5, replacement5, content)

        # 模式6: flow_date = db.Column(db.DateTime, nullable=0, default=datetime.now)
        pattern6 = r'([\w_]+)\s*=\s*db\.Column\(db\.DateTime,\s*nullable=(\d),\s*default=datetime\.now\)'
        replacement6 = r'\1 = db.Column(db.DateTime, nullable=\2, default=lambda: datetime.now().replace(microsecond=0))'
        content = re.sub(pattern6, replacement6, content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改文件: {file_path}")
            return 1
        else:
            print(f"文件无需修改: {file_path}")
            return 0
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return 0

def fix_all_model_files():
    """修改所有模型文件"""
    modified_count = 0

    for file_path in MODEL_FILES:
        print(f"\n处理文件: {file_path}")
        modified_count += fix_datetime_in_file(file_path)

    return modified_count

def main():
    """主函数"""
    print("开始修改模型定义中的时间字段...")
    modified_count = fix_all_model_files()
    print(f"\n修改完成: 共修改了 {modified_count} 个文件")

if __name__ == "__main__":
    main()
