"""
添加食材溯源模块的数据库表
"""

from app import create_app, db
from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow
from flask_migrate import Migrate

def run_migration():
    """运行迁移脚本，添加食材溯源模块的数据库表"""
    app = create_app()
    migrate = Migrate(app, db)
    
    with app.app_context():
        # 检查表是否已存在
        inspector = db.inspect(db.engine)
        existing_tables = inspector.get_table_names()
        
        tables_to_create = []
        if 'material_batches' not in existing_tables:
            tables_to_create.append(MaterialBatch.__table__)
        if 'trace_documents' not in existing_tables:
            tables_to_create.append(TraceDocument.__table__)
        if 'batch_flows' not in existing_tables:
            tables_to_create.append(BatchFlow.__table__)
        
        if tables_to_create:
            # 创建表
            db.metadata.create_all(db.engine, tables=tables_to_create)
            print("食材溯源模块的数据库表创建成功")
        else:
            print("食材溯源模块的数据库表已存在")

if __name__ == '__main__':
    run_migration()
