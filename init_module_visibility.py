"""
初始化所有角色的模块可见性设置
"""

from app import create_app, db
from app.models import Role
from app.models_visibility import ModuleVisibility
from app.utils.menu import MENU_CONFIG

def init_module_visibility():
    """初始化所有角色的模块可见性设置"""
    app = create_app()
    with app.app_context():
        # 获取所有角色
        roles = Role.query.all()
        if not roles:
            print("错误: 未找到任何角色")
            return
        
        print(f"找到 {len(roles)} 个角色")
        
        # 获取所有模块ID
        module_ids = []
        for item in MENU_CONFIG:
            module_ids.append(item['id'])
            # 添加子模块
            if 'children' in item and item['children']:
                for child in item['children']:
                    module_ids.append(child['id'])
        
        print(f"找到 {len(module_ids)} 个模块")
        
        # 为每个角色设置模块可见性
        for role in roles:
            print(f"处理角色: {role.name} (ID: {role.id})")
            
            for module_id in module_ids:
                # 检查是否已存在可见性设置
                existing = ModuleVisibility.query.filter_by(
                    module_id=module_id, 
                    role_id=role.id
                ).first()
                
                if not existing:
                    # 创建新的可见性设置
                    ModuleVisibility.set_visibility(
                        module_id=module_id,
                        role_id=role.id,
                        is_visible=1  # 默认可见
                    )
                    print(f"  创建模块可见性设置: 模块={module_id}, 角色={role.name}, 可见=1")
        
        # 保存所有更改
        db.session.commit()
        print("所有模块可见性设置已保存")

if __name__ == "__main__":
    init_module_visibility()
