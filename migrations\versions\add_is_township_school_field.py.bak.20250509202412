"""添加 is_township_school 字段到 AdministrativeArea 模型

Revision ID: add_is_township_school
Revises:
Create Date: 2023-05-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_is_township_school'
down_revision = None  # 这将在运行时自动设置
branch_labels = None
depends_on = None

# 自动查找当前的头版本并设置为 down_revision
import os
import re
from alembic import script

# 获取迁移脚本目录
migrations_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
config = script.ScriptDirectory(migrations_dir)

# 尝试获取当前的头版本
try:
    heads = config.get_heads()
    if heads and len(heads) == 1:
        down_revision = heads[0]
        print(f"自动设置 down_revision 为: {down_revision}")
    elif heads and len(heads) > 1:
        print(f"警告: 存在多个头版本: {heads}，请手动指定 down_revision")
except Exception as e:
    print(f"获取头版本时出错: {str(e)}")


def upgrade():
    """升级数据库结构，添加 is_township_school 字段"""
    # 使用 Alembic 的 op.add_column 方法添加字段
    op.add_column('administrative_areas',
                 sa.Column('is_township_school', sa.Boolean(),
                          nullable=False,
                          server_default=sa.text('0')))

    # 添加注释
    op.create_comment(
        '是否为乡镇级别直接关联的学校',
        'administrative_areas',
        'is_township_school',
        schema=None
    )


def downgrade():
    """降级数据库结构，删除 is_township_school 字段"""
    # 使用 Alembic 的 op.drop_column 方法删除字段
    op.drop_column('administrative_areas', 'is_township_school')
