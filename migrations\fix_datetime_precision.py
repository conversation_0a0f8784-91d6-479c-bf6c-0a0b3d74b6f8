"""
修复数据库中datetime字段的精度问题
"""
from app import create_app, db
from sqlalchemy import text

def run_migration():
    """运行迁移脚本，修复datetime字段的精度问题"""
    app = create_app()
    
    with app.app_context():
        # 使用原始SQL语句修改表结构
        # 修改menu_plans表
        db.session.execute(text("""
        ALTER TABLE menu_plans ALTER COLUMN created_at DATETIME2(1);
        ALTER TABLE menu_plans ALTER COLUMN updated_at DATETIME2(1);
        """))
        
        # 修改consumption_plans表
        db.session.execute(text("""
        ALTER TABLE consumption_plans ALTER COLUMN created_at DATETIME2(1);
        ALTER TABLE consumption_plans ALTER COLUMN updated_at DATETIME2(1);
        """))
        
        # 修改menu_recipes表
        db.session.execute(text("""
        ALTER TABLE menu_recipes ALTER COLUMN created_at DATETIME2(1);
        ALTER TABLE menu_recipes ALTER COLUMN updated_at DATETIME2(1);
        """))
        
        # 修改consumption_details表
        db.session.execute(text("""
        ALTER TABLE consumption_details ALTER COLUMN created_at DATETIME2(1);
        ALTER TABLE consumption_details ALTER COLUMN updated_at DATETIME2(1);
        """))
        
        # 修改food_samples表
        db.session.execute(text("""
        ALTER TABLE food_samples ALTER COLUMN destruction_time DATETIME2(1);
        ALTER TABLE food_samples ALTER COLUMN updated_at DATETIME2(1);
        """))
        
        # 提交事务
        db.session.commit()
        
        print("数据库datetime字段精度修复完成")

if __name__ == '__main__':
    run_migration()
