"""
添加 photos 表的迁移脚本
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app, db
from sqlalchemy import text

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """运行迁移脚本，添加 photos 表"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表是否已存在
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            if 'photos' not in existing_tables:
                # 创建 photos 表的 SQL
                sql = text("""
                CREATE TABLE [dbo].[photos](
                    [id] [int] IDENTITY(1,1) NOT NULL,
                    [reference_id] [int] NOT NULL,
                    [reference_type] [nvarchar](20) NOT NULL,
                    [file_name] [nvarchar](255) NOT NULL,
                    [file_path] [nvarchar](255) NOT NULL,
                    [description] [nvarchar](255) NULL,
                    [rating] [int] NULL DEFAULT 3,
                    [upload_time] [datetime2](1) NOT NULL DEFAULT GETDATE(),
                    CONSTRAINT [PK_photos] PRIMARY KEY CLUSTERED 
                    (
                        [id] ASC
                    )
                )

                -- 创建索引
                CREATE NONCLUSTERED INDEX [idx_photos_reference] ON [dbo].[photos]
                (
                    [reference_type] ASC,
                    [reference_id] ASC
                )
                """)
                
                # 执行 SQL
                db.session.execute(sql)
                db.session.commit()
                
                logger.info("photos 表创建成功")
            else:
                logger.info("photos 表已存在")
                
            # 检查是否需要添加 rating 字段
            columns = inspector.get_columns('photos')
            column_names = [col['name'] for col in columns]
            
            if 'rating' not in column_names:
                # 添加 rating 字段
                sql = text("""
                ALTER TABLE [dbo].[photos]
                ADD [rating] [int] NULL DEFAULT 3
                """)
                
                # 执行 SQL
                db.session.execute(sql)
                db.session.commit()
                
                logger.info("photos 表添加 rating 字段成功")
                
        except Exception as e:
            db.session.rollback()
            logger.error(f"迁移失败: {str(e)}")
            raise

if __name__ == '__main__':
    run_migration()
