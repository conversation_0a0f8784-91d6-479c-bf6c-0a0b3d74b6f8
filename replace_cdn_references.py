#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
替换项目中的 CDN 引用为本地资源
"""

import os
import re
import sys
from colorama import init, Fore, Style

# 初始化 colorama
init()

# CDN 替换映射
CDN_REPLACEMENTS = {
    # Select2
    r'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css': 
        "{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}",
    r'https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@[^/]+/dist/select2-bootstrap4.min.css': 
        "{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}",
    r'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js': 
        "{{ url_for('static', filename='vendor/select2/select2.min.js') }}",
    
    # Chart.js
    r'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js': 
        "{{ url_for('static', filename='vendor/chart-js/chart.min.js') }}",
    
    # Moment.js
    r'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js': 
        "{{ url_for('static', filename='vendor/moment/moment.min.js') }}",
    
    # Tempusdominus Bootstrap 4
    r'https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css': 
        "{{ url_for('static', filename='vendor/tempusdominus/css/tempusdominus-bootstrap-4.min.css') }}",
    r'https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/js/tempusdominus-bootstrap-4.min.js': 
        "{{ url_for('static', filename='vendor/tempusdominus/js/tempusdominus-bootstrap-4.min.js') }}",
    
    # bs-custom-file-input
    r'https://cdn.jsdelivr.net/npm/bs-custom-file-input/dist/bs-custom-file-input.min.js': 
        "{{ url_for('static', filename='vendor/bs-custom-file-input/bs-custom-file-input.min.js') }}",
    
    # SortableJS
    r'https://cdn.jsdelivr.net/npm/sortablejs@[^/]+/Sortable.min.js': 
        "{{ url_for('static', filename='vendor/sortablejs/Sortable.min.js') }}",
    
    # SweetAlert2
    r'https://cdn.jsdelivr.net/npm/sweetalert2@11(/dist/sweetalert2.min.js)?': 
        "{{ url_for('static', filename='vendor/sweetalert2/sweetalert2.min.js') }}",
    r'https://cdn.jsdelivr.net/npm/@sweetalert2/theme-bootstrap-4/bootstrap-4.css': 
        "{{ url_for('static', filename='vendor/sweetalert2/css/bootstrap-4.css') }}",
    
    # jsTree
    r'https://cdn.jsdelivr.net/npm/jstree@3.3.11/dist/jstree.min.js': 
        "{{ url_for('static', filename='vendor/jstree/jstree.min.js') }}",
    r'https://cdn.jsdelivr.net/npm/jstree@3.3.11/dist/themes/default/style.min.css': 
        "{{ url_for('static', filename='vendor/jstree/themes/default/style.min.css') }}",
    
    # jQuery UI
    r'https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css': 
        "{{ url_for('static', filename='vendor/jquery-ui/css/jquery-ui.min.css') }}",
    r'https://code.jquery.com/ui/1.12.1/jquery-ui.min.js': 
        "{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}",
    
    # Bootstrap
    r'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css': 
        "{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}",
    r'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js': 
        "{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}",
    
    # Font Awesome
    r'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.3/css/all.min.css': 
        "{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}",
    
    # jQuery
    r'https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js': 
        "{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}",
    
    # Toastr
    r'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css': 
        "{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}",
    r'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js': 
        "{{ url_for('static', filename='vendor/toastr/toastr.min.js') }}"
}

# 要检查的文件扩展名
FILE_EXTENSIONS = ['.html']

# 要检查的目录
DIRECTORIES = ['app/templates']

# 要排除的目录
EXCLUDE_DIRS = ['venv', 'node_modules', 'bootstrap-4.6.2', 'bootstrap-4.6.2.BAK', '__pycache__']

def replace_cdn_references(file_path):
    """替换文件中的 CDN 引用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except UnicodeDecodeError:
            print(f"{Fore.YELLOW}警告: 无法读取文件 {file_path} (编码问题){Style.RESET_ALL}")
            return False
    
    original_content = content
    
    # 替换 CDN 引用
    for cdn_pattern, local_path in CDN_REPLACEMENTS.items():
        content = re.sub(cdn_pattern, local_path, content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"{Fore.RED}错误: 无法写入文件 {file_path}: {str(e)}{Style.RESET_ALL}")
            return False
    
    return False

def process_directory(directory):
    """处理目录中的文件"""
    modified_files = []
    
    for root, dirs, files in os.walk(directory):
        # 排除指定目录
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
        
        for file in files:
            if any(file.endswith(ext) for ext in FILE_EXTENSIONS):
                file_path = os.path.join(root, file)
                if replace_cdn_references(file_path):
                    modified_files.append(file_path)
    
    return modified_files

def main():
    """主函数"""
    print(f"{Fore.CYAN}开始替换项目中的 CDN 引用...{Style.RESET_ALL}")
    
    all_modified_files = []
    for directory in DIRECTORIES:
        if os.path.exists(directory):
            print(f"{Fore.BLUE}正在处理目录: {directory}{Style.RESET_ALL}")
            modified_files = process_directory(directory)
            all_modified_files.extend(modified_files)
        else:
            print(f"{Fore.YELLOW}警告: 目录 {directory} 不存在{Style.RESET_ALL}")
    
    if all_modified_files:
        print(f"\n{Fore.GREEN}已修改 {len(all_modified_files)} 个文件:{Style.RESET_ALL}")
        for file_path in all_modified_files:
            print(f"  {file_path}")
        
        print(f"\n{Fore.GREEN}总计: {len(all_modified_files)} 个文件已更新{Style.RESET_ALL}")
        return 0
    else:
        print(f"\n{Fore.YELLOW}未发现需要修改的文件。{Style.RESET_ALL}")
        return 0

if __name__ == "__main__":
    sys.exit(main())
