"""
修复项目中的datetime精度问题

此脚本将：
1. 只扫描项目中的app目录下的Python文件
2. 查找所有使用db.DateTime但未指定精度的地方
3. 将它们替换为DATETIME2(precision=1)
4. 保留已经指定了精度的DATETIME2(precision=1)不变
"""

import os
import re
import sys
from datetime import datetime

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 需要扫描的目录列表（相对于项目根目录）
TARGET_DIRS = [
    'app',
    'migrations'
]

# 需要排除的目录列表（相对于项目根目录）
EXCLUDE_DIRS = [
    'venv',
    '.venv',
    '__pycache__',
    '.git'
]

def backup_file(file_path):
    """备份文件"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        try:
            with open(file_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            print(f"已备份文件: {file_path} -> {backup_path}")
            return True
        except Exception as e:
            print(f"备份文件时出错: {str(e)}")
            return False
    return False

def fix_datetime_in_file(file_path, precision=1):
    """修改文件中的DateTime字段定义"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 查找所有的DateTime字段定义
        # 模式: db.Column(db.DateTime, ...) 但不匹配 db.Column(db.DateTime(...), ...)
        pattern = r'db\.Column\(db\.DateTime(?!\s*\()'

        # 替换为带精度的版本
        # db.Column(db.DateTime, ...) -> db.Column(DATETIME2(precision=1), ...)
        replacement = f'db.Column(db.DateTime({precision})'

        # 应用替换
        content = re.sub(pattern, replacement, content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改文件: {file_path}")
            return 1
        else:
            print(f"文件无需修改: {file_path}")
            return 0

    except Exception as e:
        print(f"修改文件时出错: {str(e)}")
        return 0

def should_process_directory(dir_path):
    """判断是否应该处理该目录"""
    # 检查是否是排除目录
    for exclude_dir in EXCLUDE_DIRS:
        exclude_path = os.path.join(PROJECT_ROOT, exclude_dir)
        if dir_path.startswith(exclude_path):
            return False
    
    # 检查是否是目标目录
    for target_dir in TARGET_DIRS:
        target_path = os.path.join(PROJECT_ROOT, target_dir)
        if dir_path.startswith(target_path):
            return True
    
    return False

def scan_directories(precision=1):
    """扫描指定目录中的所有Python文件并修复DateTime精度"""
    modified_count = 0
    
    for target_dir in TARGET_DIRS:
        dir_path = os.path.join(PROJECT_ROOT, target_dir)
        if not os.path.exists(dir_path):
            print(f"目录不存在: {dir_path}")
            continue
            
        print(f"\n扫描目录: {dir_path}")
        
        for root, dirs, files in os.walk(dir_path):
            # 跳过排除目录
            if not should_process_directory(root):
                continue
                
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    # 备份文件
                    if backup_file(file_path):
                        # 修复文件
                        if fix_datetime_in_file(file_path, precision):
                            modified_count += 1
    
    return modified_count

def main():
    """主函数"""
    print("开始修复项目中的datetime精度问题...")
    
    # 设置精度
    precision = 1
    
    # 扫描并修复项目中的Python文件
    modified_count = scan_directories(precision)
    
    print(f"\n修复完成，共修改了 {modified_count} 个文件")
    print("请重启应用程序以应用更改")

if __name__ == "__main__":
    main()
