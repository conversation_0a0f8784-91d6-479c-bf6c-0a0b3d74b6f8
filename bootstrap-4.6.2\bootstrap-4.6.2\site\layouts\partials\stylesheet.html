{{- "<!-- Bootstrap core CSS -->" | safeHTML }}
{{ if eq hugo.Environment "production" -}}
<link href="/docs/{{ .Site.Params.docs_version }}/dist/css/bootstrap.min.css" rel="stylesheet" {{ printf "integrity=%q" .Site.Params.cdn.css_hash | safeHTMLAttr }} crossorigin="anonymous">
{{- else -}}
<link href="/docs/{{ .Site.Params.docs_version }}/dist/css/bootstrap.css" rel="stylesheet">
{{- end }}

{{ if (or (eq .Page.Layout "docs") (eq .Page.Layout "single")) -}}
{{- "<!-- Documentation extras -->" | safeHTML }}
<link href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" rel="stylesheet">
{{- end -}}

{{- if (ne .<PERSON>.Layout "examples") }}
{{- $targetDocsCssPath := path.Join "/docs" .Site.Params.docs_version "assets/css/docs.css" -}}
{{- $sassOptions := dict "targetPath" $targetDocsCssPath "outputStyle" "expanded" "precision" 6 -}}
{{- $postcssOptions := dict "use" "autoprefixer" "noMap" true -}}

{{ if eq hugo.Environment "production" -}}
  {{- $sassOptions = merge $sassOptions (dict "outputStyle" "compressed") -}}
{{- end -}}

{{- $style := resources.Get "scss/docs.scss" | toCSS $sassOptions | postCSS $postcssOptions }}

<link href="{{ $style.Permalink | relURL }}" rel="stylesheet">
{{- end }}
