-- 直接创建 inspection_records 表，不使用约束名称
CREATE TABLE inspection_records (
    id INT PRIMARY KEY IDENTITY(1,1),
    daily_log_id INT NOT NULL,
    inspection_type VARCHAR(10) NOT NULL,
    inspection_item VARCHAR(100) NOT NULL,
    status VARCHAR(10) DEFAULT 'normal',
    description NVARCHAR(MAX),
    inspector_id INT,
    inspection_time DATETIME2(1) DEFAULT GETDATE(),
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id),
    FOREIGN KEY (inspector_id) REFERENCES users(id),
    CHECK (inspection_type IN ('morning', 'noon', 'evening')),
    CHECK (status IN ('normal', 'abnormal'))
);
