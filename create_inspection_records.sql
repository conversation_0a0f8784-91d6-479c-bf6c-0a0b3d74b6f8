-- 检查 inspection_records 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'inspection_records')
BEGIN
    -- 创建 inspection_records 表
    CREATE TABLE inspection_records (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        inspection_type VARCHAR(10) NOT NULL,
        inspection_item VARCHAR(100) NOT NULL,
        status VARCHAR(10) DEFAULT 'normal',
        description NVARCHAR(MAX),
        inspector_id INT,
        inspection_time DATETIME2(1) DEFAULT GETDATE(),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_inspection_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id),
        CONSTRAINT FK_inspection_inspector FOREIGN KEY (inspector_id) REFERENCES users(id),
        CONSTRAINT CK_inspection_type CHECK (inspection_type IN ('morning', 'noon', 'evening')),
        CONSTRAINT CK_inspection_status CHECK (status IN ('normal', 'abnormal'))
    );
    
    PRINT 'inspection_records 表已创建';
END
ELSE
BEGIN
    PRINT 'inspection_records 表已存在';
END
