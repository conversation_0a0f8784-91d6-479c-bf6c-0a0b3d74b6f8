"""
修复models.py文件中的DATETIME2问题

此脚本用于修复models.py文件中的DATETIME2问题，包括：
1. 修复DATETIME2导入语句
2. 修复DATETIME2使用方式
3. 修复datetime.now()使用方式
"""
import re
import os
import sys
import logging
from datetime import datetime

# 配置日志
log_file = f"fix_models_datetime_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def fix_models_file(file_path):
    """修复models.py文件中的DATETIME2问题"""
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已备份原文件: {backup_path}")
        
        # 1. 修复DATETIME2导入语句
        content = re.sub(
            r'from sqlalchemy\.dialects\.mssql import DATETIME2\(.*?\)',
            'from sqlalchemy.dialects.mssql import DATETIME2',
            content
        )
        
        # 2. 修复DATETIME2使用方式
        content = re.sub(
            r'DATETIME2\(precision=1, precision=1(?:, precision=1)?\)',
            'DATETIME2(precision=1)',
            content
        )
        
        # 3. 修复datetime.now()使用方式
        content = re.sub(
            r'datetime\.now\(\)\.replace\(microsecond=0\)\.replace\(microsecond=0\)(?:\.replace\(microsecond=0\))?',
            'datetime.now().replace(microsecond=0)',
            content
        )
        
        # 写入修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"已修复文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"修复文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "修复models.py文件中的DATETIME2问题")
    print("="*80)
    print(f"\n日志文件: {log_file}")
    
    # 修复models.py文件
    models_files = [
        'app/models.py',
        'app/models_daily_management.py',
        'app/models_phase4.py'
    ]
    
    for file_path in models_files:
        if os.path.exists(file_path):
            print(f"\n修复文件: {file_path}")
            if fix_models_file(file_path):
                print(f"  ✓ 文件修复成功")
            else:
                print(f"  ✗ 文件修复失败，请查看日志")
        else:
            print(f"\n文件不存在，跳过: {file_path}")
    
    print("\n修复完成！")
    print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
