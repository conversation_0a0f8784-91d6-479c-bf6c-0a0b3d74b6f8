"""
修复采购员角色权限和模块可见性的脚本
"""

from app import create_app, db
from app.models import Role
from app.models_visibility import ModuleVisibility
import json

def fix_purchase_role():
    """修复采购员角色的权限和模块可见性"""
    app = create_app()
    with app.app_context():
        # 1. 获取采购员角色
        purchase_role = Role.query.filter_by(name='采购员').first()
        if not purchase_role:
            print("错误: 未找到采购员角色")
            return
        
        role_id = purchase_role.id
        print(f"找到采购员角色，ID: {role_id}")
        
        # 2. 添加模块可见性设置
        module_id = 'purchase'
        
        # 检查是否已存在可见性设置
        existing = ModuleVisibility.query.filter_by(
            module_id=module_id, 
            role_id=role_id
        ).first()
        
        if existing:
            # 更新现有设置
            existing.is_visible = 1
            print(f"更新现有可见性设置: 模块={module_id}, 角色={role_id}, 可见=1")
        else:
            # 创建新的可见性设置
            ModuleVisibility.set_visibility(
                module_id=module_id,
                role_id=role_id,
                is_visible=1
            )
            print(f"创建新的可见性设置: 模块={module_id}, 角色={role_id}, 可见=1")
        
        # 3. 更新角色权限
        current_permissions = json.loads(purchase_role.permissions) if purchase_role.permissions else {}
        
        # 添加采购模块的查看和创建权限
        if 'purchase' not in current_permissions:
            current_permissions['purchase'] = []
        
        if 'view' not in current_permissions['purchase']:
            current_permissions['purchase'].append('view')
        
        if 'create' not in current_permissions['purchase']:
            current_permissions['purchase'].append('create')
        
        if 'edit' not in current_permissions['purchase']:
            current_permissions['purchase'].append('edit')
        
        # 更新角色权限
        purchase_role.permissions = json.dumps(current_permissions)
        
        # 4. 保存更改
        db.session.commit()
        
        print(f"采购员角色权限已更新: {purchase_role.permissions}")
        print("所有修改已保存")

        # 5. 检查子模块可见性
        child_modules = ['purchase_order_list', 'purchase_order_create', 'supplier_list', 'supplier_product_list']
        for child_id in child_modules:
            # 检查是否已存在可见性设置
            existing = ModuleVisibility.query.filter_by(
                module_id=child_id, 
                role_id=role_id
            ).first()
            
            if existing:
                # 更新现有设置
                existing.is_visible = 1
                print(f"更新现有可见性设置: 子模块={child_id}, 角色={role_id}, 可见=1")
            else:
                # 创建新的可见性设置
                ModuleVisibility.set_visibility(
                    module_id=child_id,
                    role_id=role_id,
                    is_visible=1
                )
                print(f"创建新的可见性设置: 子模块={child_id}, 角色={role_id}, 可见=1")
        
        # 保存子模块可见性设置
        db.session.commit()
        print("子模块可见性设置已保存")

if __name__ == "__main__":
    fix_purchase_role()
