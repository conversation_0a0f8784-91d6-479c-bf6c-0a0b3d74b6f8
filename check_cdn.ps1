# 检查项目中是否还有使用 CDN 的地方
$cdnPatterns = @("cdn.jsdelivr.net", "cdnjs.cloudflare.com", "code.jquery.com", "fonts.googleapis.com", "maxcdn.bootstrapcdn.com", "stackpath.bootstrapcdn.com", "unpkg.com")

$templateDir = "app/templates"
$staticDir = "app/static"

Write-Host "正在检查模板文件中的 CDN 引用..."
$templateFiles = Get-ChildItem -Path $templateDir -Recurse -Include "*.html"
foreach ($file in $templateFiles) {
    $content = Get-Content -Path $file.FullName -Raw
    foreach ($pattern in $cdnPatterns) {
        if ($content -match $pattern) {
            Write-Host "在文件 $($file.FullName) 中找到 CDN 引用: $pattern"
        }
    }
}

Write-Host "`n正在检查静态文件中的 CDN 引用..."
$staticFiles = Get-ChildItem -Path $staticDir -Recurse -Include "*.js", "*.css"
foreach ($file in $staticFiles) {
    $content = Get-Content -Path $file.FullName -Raw
    foreach ($pattern in $cdnPatterns) {
        if ($content -match $pattern) {
            Write-Host "在文件 $($file.FullName) 中找到 CDN 引用: $pattern"
        }
    }
}

Write-Host "`n检查完成！"
