"""
修改所有时间字段，使其只精确到分钟级别

此脚本将执行以下操作：
1. 修改数据库中的时间字段，使其只精确到分钟级别（精度设置为1）
2. 修改模型定义中的时间字段，使其在创建时就只精确到分钟级别
3. 修改应用程序中的时间格式化函数，确保它们在显示时也只显示到分钟级别

注意：SQL Server不支持精度为0的DATETIME2类型，最小精度为1。
"""

import os
import sys
import importlib.util
import time

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

def import_module_from_file(file_path):
    """从文件导入模块"""
    module_name = os.path.basename(file_path).replace('.py', '')
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def run_script(script_name):
    """运行指定的脚本"""
    print(f"\n{'='*50}")
    print(f"执行脚本: {script_name}")
    print(f"{'='*50}")

    script_path = os.path.join(PROJECT_ROOT, script_name)

    if not os.path.exists(script_path):
        print(f"脚本不存在: {script_path}")
        return False

    try:
        module = import_module_from_file(script_path)
        if hasattr(module, 'main'):
            module.main()
            return True
        else:
            print(f"脚本 {script_name} 没有main函数")
            return False
    except Exception as e:
        print(f"执行脚本 {script_name} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("开始修改所有时间字段，使其只精确到分钟级别...")

    # 步骤1: 修改数据库中的时间字段
    print("\n步骤1: 修改数据库中的时间字段")
    if not run_script('fix_datetime_to_minute.py'):
        print("步骤1失败，继续执行后续步骤")

    # 等待一段时间，确保数据库操作完成
    time.sleep(2)

    # 步骤2: 修改模型定义中的时间字段
    print("\n步骤2: 修改模型定义中的时间字段")
    if not run_script('fix_model_datetime_to_minute.py'):
        print("步骤2失败，继续执行后续步骤")

    # 步骤3: 修改应用程序中的时间格式化函数
    print("\n步骤3: 修改应用程序中的时间格式化函数")
    if not run_script('fix_datetime_format.py'):
        print("步骤3失败")

    print("\n所有修改操作已完成")
    print("现在所有时间字段都只精确到分钟级别")

if __name__ == "__main__":
    main()
