"""
DATETIME2字段精度修复主程序

此脚本提供一个交互式界面，允许用户选择要执行的修复操作：
1. 分析数据库并生成修复脚本
2. 修复食堂日常管理模块
3. 修复库存管理模块
4. 修复员工管理模块
5. 综合修复所有模块
"""
import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"datetime_fix_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_header():
    """打印程序头部信息"""
    print("\n" + "="*80)
    print(" "*30 + "DATETIME2字段精度修复工具")
    print("="*80)
    print("\n此工具用于修复数据库中DATETIME2字段的精度问题。")
    print("请选择要执行的操作：\n")

def print_menu():
    """打印菜单选项"""
    print("1. 分析数据库并生成修复脚本")
    print("2. 修复食堂日常管理模块")
    print("3. 修复库存管理模块")
    print("4. 修复员工管理模块")
    print("5. 综合修复所有模块")
    print("0. 退出程序")
    print("\n请输入选项编号：", end="")

def run_analysis():
    """运行数据库分析"""
    try:
        logger.info("开始分析数据库...")
        from analyze_datetime_fields import analyze_database
        analyze_database()
        logger.info("数据库分析完成")
    except Exception as e:
        logger.error(f"分析数据库时出错: {str(e)}")
        print(f"错误: {str(e)}")

def run_daily_management_fix():
    """修复食堂日常管理模块"""
    try:
        logger.info("开始修复食堂日常管理模块...")
        from fix_comprehensive_datetime_precision import fix_daily_management_datetime_precision

        # 直接导入app实例，而不是create_app函数
        try:
            from app import app
        except ImportError:
            # 如果无法直接导入app实例，则尝试使用create_app
            from app import create_app
            app = create_app()

            # 禁用可能导致问题的扩展
            if hasattr(app, 'extensions') and 'admin' in app.extensions:
                del app.extensions['admin']

        with app.app_context():
            if fix_daily_management_datetime_precision():
                print("成功修复食堂日常管理模块的 DATETIME2 字段精度问题")
                logger.info("食堂日常管理模块修复成功")
            else:
                print("修复食堂日常管理模块时出错，请查看日志")
                logger.warning("食堂日常管理模块修复失败")
    except Exception as e:
        logger.error(f"修复食堂日常管理模块时出错: {str(e)}")
        print(f"错误: {str(e)}")

def run_inventory_fix():
    """修复库存管理模块"""
    try:
        logger.info("开始修复库存管理模块...")
        from fix_comprehensive_datetime_precision import fix_inventory_datetime_precision

        # 直接导入app实例，而不是create_app函数
        try:
            from app import app
        except ImportError:
            # 如果无法直接导入app实例，则尝试使用create_app
            from app import create_app
            app = create_app()

            # 禁用可能导致问题的扩展
            if hasattr(app, 'extensions') and 'admin' in app.extensions:
                del app.extensions['admin']

        with app.app_context():
            if fix_inventory_datetime_precision():
                print("成功修复库存管理模块的 DATETIME2 字段精度问题")
                logger.info("库存管理模块修复成功")
            else:
                print("修复库存管理模块时出错，请查看日志")
                logger.warning("库存管理模块修复失败")
    except Exception as e:
        logger.error(f"修复库存管理模块时出错: {str(e)}")
        print(f"错误: {str(e)}")

def run_employee_fix():
    """修复员工管理模块"""
    try:
        logger.info("开始修复员工管理模块...")
        from fix_comprehensive_datetime_precision import fix_employee_datetime_precision

        # 直接导入app实例，而不是create_app函数
        try:
            from app import app
        except ImportError:
            # 如果无法直接导入app实例，则尝试使用create_app
            from app import create_app
            app = create_app()

            # 禁用可能导致问题的扩展
            if hasattr(app, 'extensions') and 'admin' in app.extensions:
                del app.extensions['admin']

        with app.app_context():
            if fix_employee_datetime_precision():
                print("成功修复员工管理模块的 DATETIME2 字段精度问题")
                logger.info("员工管理模块修复成功")
            else:
                print("修复员工管理模块时出错，请查看日志")
                logger.warning("员工管理模块修复失败")
    except Exception as e:
        logger.error(f"修复员工管理模块时出错: {str(e)}")
        print(f"错误: {str(e)}")

def run_comprehensive_fix():
    """综合修复所有模块"""
    try:
        logger.info("开始综合修复所有模块...")
        from fix_comprehensive_datetime_precision import fix_comprehensive_datetime_precision

        # 直接导入app实例，而不是create_app函数
        try:
            from app import app
        except ImportError:
            # 如果无法直接导入app实例，则尝试使用create_app
            from app import create_app
            app = create_app()

            # 禁用可能导致问题的扩展
            if hasattr(app, 'extensions') and 'admin' in app.extensions:
                del app.extensions['admin']

        with app.app_context():
            if fix_comprehensive_datetime_precision():
                print("成功修复所有模块的 DATETIME2 字段精度问题")
                logger.info("所有模块修复成功")
            else:
                print("修复过程中出现错误，请查看日志")
                logger.warning("部分或全部模块修复失败")
    except Exception as e:
        logger.error(f"综合修复时出错: {str(e)}")
        print(f"错误: {str(e)}")

def main():
    """主函数"""
    print_header()

    while True:
        print_menu()
        choice = input().strip()

        if choice == '0':
            print("\n感谢使用，再见！")
            break
        elif choice == '1':
            run_analysis()
        elif choice == '2':
            run_daily_management_fix()
        elif choice == '3':
            run_inventory_fix()
        elif choice == '4':
            run_employee_fix()
        elif choice == '5':
            run_comprehensive_fix()
        else:
            print("\n无效的选项，请重新输入！")

        input("\n按Enter键继续...")
        print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {str(e)}")
        print(f"\n程序发生错误: {str(e)}")
        print("详细信息已记录到日志文件中")
    finally:
        print("\n程序已退出")
