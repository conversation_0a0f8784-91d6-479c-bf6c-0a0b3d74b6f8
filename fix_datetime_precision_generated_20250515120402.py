"""
自动生成的DATETIME2字段精度修复脚本 - 生成时间: 2025-05-15 12:04:02

此脚本使用系统自带的框架修复数据库中所有表的 DATETIME2 字段精度问题。
"""
from app import db, create_app
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_datetime_precision():
    """修复所有表的 DATETIME2 字段精度问题"""
    try:
        # 修复 administrative_areas 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'administrative_areas' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE administrative_areas 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'administrative_areas 表的 created_at 字段已修复';
END
"""))

        # 修复 administrative_areas_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'administrative_areas_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE administrative_areas_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'administrative_areas_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 area_change_history 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'area_change_history' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE area_change_history 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'area_change_history 表的 created_at 字段已修复';
END
"""))

        # 修复 area_change_history_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'area_change_history_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE area_change_history_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'area_change_history_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 area_change_history_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'area_change_history_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE area_change_history_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'area_change_history_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 audit_logs 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'audit_logs' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE audit_logs 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'audit_logs 表的 created_at 字段已修复';
END
"""))

        # 修复 audit_logs_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'audit_logs_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE audit_logs_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'audit_logs_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 audit_logs_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'audit_logs_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE audit_logs_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'audit_logs_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 batch_flows 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'batch_flows' 
    AND COLUMN_NAME = 'flow_date'
)
BEGIN
    ALTER TABLE batch_flows 
    ALTER COLUMN flow_date DATETIME2(1) NOT NULL;
    
    PRINT 'batch_flows 表的 flow_date 字段已修复';
END
"""))

        # 修复 batch_flows 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'batch_flows' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE batch_flows 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'batch_flows 表的 created_at 字段已修复';
END
"""))

        # 修复 cafeteria_areas 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'cafeteria_areas' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE cafeteria_areas 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'cafeteria_areas 表的 created_at 字段已修复';
END
"""))

        # 修复 cafeteria_areas 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'cafeteria_areas' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE cafeteria_areas 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'cafeteria_areas 表的 updated_at 字段已修复';
END
"""))

        # 修复 canteen_training_records 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'canteen_training_records' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE canteen_training_records 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'canteen_training_records 表的 created_at 字段已修复';
END
"""))

        # 修复 canteen_training_records 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'canteen_training_records' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE canteen_training_records 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'canteen_training_records 表的 updated_at 字段已修复';
END
"""))

        # 修复 category_unit_mappings 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'category_unit_mappings' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE category_unit_mappings 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'category_unit_mappings 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_details 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_details' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE consumption_details 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_details 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_details 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_details' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE consumption_details 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_details 表的 updated_at 字段已修复';
END
"""))

        # 修复 consumption_details_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_details_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE consumption_details_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_details_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_details_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_details_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE consumption_details_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_details_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 consumption_details_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_details_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE consumption_details_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_details_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_details_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_details_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE consumption_details_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_details_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 consumption_plans 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE consumption_plans 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_plans 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_plans 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE consumption_plans 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_plans 表的 updated_at 字段已修复';
END
"""))

        # 修复 consumption_plans_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE consumption_plans_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_plans_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_plans_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE consumption_plans_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_plans_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 consumption_plans_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE consumption_plans_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_plans_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 consumption_plans_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'consumption_plans_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE consumption_plans_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'consumption_plans_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 daily_health_checks 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'daily_health_checks' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE daily_health_checks 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'daily_health_checks 表的 created_at 字段已修复';
END
"""))

        # 修复 daily_logs 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'daily_logs' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE daily_logs 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'daily_logs 表的 created_at 字段已修复';
END
"""))

        # 修复 daily_logs 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'daily_logs' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE daily_logs 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'daily_logs 表的 updated_at 字段已修复';
END
"""))

        # 修复 delivery_inspections 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_inspections' 
    AND COLUMN_NAME = 'inspection_date'
)
BEGIN
    ALTER TABLE delivery_inspections 
    ALTER COLUMN inspection_date DATETIME2(1) NULL;
    
    PRINT 'delivery_inspections 表的 inspection_date 字段已修复';
END
"""))

        # 修复 delivery_inspections 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_inspections' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE delivery_inspections 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'delivery_inspections 表的 created_at 字段已修复';
END
"""))

        # 修复 delivery_item_inspections 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_item_inspections' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE delivery_item_inspections 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_item_inspections 表的 created_at 字段已修复';
END
"""))

        # 修复 delivery_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_items' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE delivery_items 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_items 表的 created_at 字段已修复';
END
"""))

        # 修复 delivery_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_items' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE delivery_items 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_items 表的 updated_at 字段已修复';
END
"""))

        # 修复 delivery_items_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_items_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE delivery_items_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_items_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 delivery_items_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_items_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE delivery_items_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_items_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 delivery_items_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_items_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE delivery_items_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_items_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 delivery_items_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'delivery_items_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE delivery_items_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'delivery_items_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 dining_companions 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'dining_companions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE dining_companions 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'dining_companions 表的 created_at 字段已修复';
END
"""))

        # 修复 dining_companions 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'dining_companions' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE dining_companions 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'dining_companions 表的 updated_at 字段已修复';
END
"""))

        # 修复 employees 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'employees' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE employees 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'employees 表的 created_at 字段已修复';
END
"""))

        # 修复 employees 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'employees' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE employees 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'employees 表的 updated_at 字段已修复';
END
"""))

        # 修复 employees_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'employees_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE employees_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'employees_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 employees_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'employees_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE employees_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'employees_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 employees_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'employees_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE employees_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'employees_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 employees_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'employees_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE employees_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'employees_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 food_samples 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'food_samples' 
    AND COLUMN_NAME = 'start_time'
)
BEGIN
    ALTER TABLE food_samples 
    ALTER COLUMN start_time DATETIME2(1) NOT NULL;
    
    PRINT 'food_samples 表的 start_time 字段已修复';
END
"""))

        # 修复 food_samples 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'food_samples' 
    AND COLUMN_NAME = 'end_time'
)
BEGIN
    ALTER TABLE food_samples 
    ALTER COLUMN end_time DATETIME2(1) NOT NULL;
    
    PRINT 'food_samples 表的 end_time 字段已修复';
END
"""))

        # 修复 food_samples 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'food_samples' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE food_samples 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'food_samples 表的 created_at 字段已修复';
END
"""))

        # 修复 food_samples 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'food_samples' 
    AND COLUMN_NAME = 'destruction_time'
)
BEGIN
    ALTER TABLE food_samples 
    ALTER COLUMN destruction_time DATETIME2(1) NULL;
    
    PRINT 'food_samples 表的 destruction_time 字段已修复';
END
"""))

        # 修复 food_samples 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'food_samples' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE food_samples 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'food_samples 表的 updated_at 字段已修复';
END
"""))

        # 修复 health_certificates 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'health_certificates' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE health_certificates 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'health_certificates 表的 created_at 字段已修复';
END
"""))

        # 修复 health_certificates 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'health_certificates' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE health_certificates 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'health_certificates 表的 updated_at 字段已修复';
END
"""))

        # 修复 health_certificates_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'health_certificates_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE health_certificates_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'health_certificates_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 health_certificates_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'health_certificates_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE health_certificates_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'health_certificates_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 health_certificates_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'health_certificates_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE health_certificates_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'health_certificates_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 health_certificates_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'health_certificates_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE health_certificates_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'health_certificates_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 ingredient_categories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredient_categories' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE ingredient_categories 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredient_categories 表的 created_at 字段已修复';
END
"""))

        # 修复 ingredient_categories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredient_categories' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE ingredient_categories 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredient_categories 表的 updated_at 字段已修复';
END
"""))

        # 修复 ingredient_categories_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredient_categories_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE ingredient_categories_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredient_categories_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 ingredient_categories_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredient_categories_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE ingredient_categories_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredient_categories_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 ingredient_categories_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredient_categories_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE ingredient_categories_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredient_categories_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 ingredient_categories_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredient_categories_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE ingredient_categories_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredient_categories_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 ingredients 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredients' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE ingredients 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredients 表的 created_at 字段已修复';
END
"""))

        # 修复 ingredients 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredients' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE ingredients 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredients 表的 updated_at 字段已修复';
END
"""))

        # 修复 ingredients_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredients_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE ingredients_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredients_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 ingredients_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredients_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE ingredients_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredients_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 ingredients_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredients_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE ingredients_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredients_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 ingredients_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ingredients_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE ingredients_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'ingredients_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 inspection_records 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inspection_records' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE inspection_records 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'inspection_records 表的 created_at 字段已修复';
END
"""))

        # 修复 inspection_records 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inspection_records' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE inspection_records 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'inspection_records 表的 updated_at 字段已修复';
END
"""))

        # 修复 inventories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventories' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE inventories 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventories 表的 created_at 字段已修复';
END
"""))

        # 修复 inventories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventories' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE inventories 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventories 表的 updated_at 字段已修复';
END
"""))

        # 修复 inventory_alerts 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_alerts' 
    AND COLUMN_NAME = 'processed_at'
)
BEGIN
    ALTER TABLE inventory_alerts 
    ALTER COLUMN processed_at DATETIME2(1) NULL;
    
    PRINT 'inventory_alerts 表的 processed_at 字段已修复';
END
"""))

        # 修复 inventory_alerts 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_alerts' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE inventory_alerts 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventory_alerts 表的 created_at 字段已修复';
END
"""))

        # 修复 inventory_alerts 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_alerts' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE inventory_alerts 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventory_alerts 表的 updated_at 字段已修复';
END
"""))

        # 修复 inventory_check_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_check_items' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE inventory_check_items 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventory_check_items 表的 created_at 字段已修复';
END
"""))

        # 修复 inventory_check_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_check_items' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE inventory_check_items 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventory_check_items 表的 updated_at 字段已修复';
END
"""))

        # 修复 inventory_checks 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_checks' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE inventory_checks 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventory_checks 表的 created_at 字段已修复';
END
"""))

        # 修复 inventory_checks 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'inventory_checks' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE inventory_checks 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'inventory_checks 表的 updated_at 字段已修复';
END
"""))

        # 修复 issues 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'issues' 
    AND COLUMN_NAME = 'found_time'
)
BEGIN
    ALTER TABLE issues 
    ALTER COLUMN found_time DATETIME2(1) NOT NULL;
    
    PRINT 'issues 表的 found_time 字段已修复';
END
"""))

        # 修复 issues 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'issues' 
    AND COLUMN_NAME = 'fixed_time'
)
BEGIN
    ALTER TABLE issues 
    ALTER COLUMN fixed_time DATETIME2(1) NULL;
    
    PRINT 'issues 表的 fixed_time 字段已修复';
END
"""))

        # 修复 issues 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'issues' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE issues 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'issues 表的 created_at 字段已修复';
END
"""))

        # 修复 issues 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'issues' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE issues 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'issues 表的 updated_at 字段已修复';
END
"""))

        # 修复 material_batches 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'material_batches' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE material_batches 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'material_batches 表的 created_at 字段已修复';
END
"""))

        # 修复 material_batches 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'material_batches' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE material_batches 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'material_batches 表的 updated_at 字段已修复';
END
"""))

        # 修复 medical_examinations 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'medical_examinations' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE medical_examinations 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'medical_examinations 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_plans 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE menu_plans 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_plans 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_plans 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE menu_plans 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_plans 表的 updated_at 字段已修复';
END
"""))

        # 修复 menu_plans_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE menu_plans_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_plans_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_plans_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE menu_plans_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_plans_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 menu_plans_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE menu_plans_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_plans_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_plans_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_plans_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE menu_plans_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_plans_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 menu_recipes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_recipes' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE menu_recipes 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_recipes 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_recipes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_recipes' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE menu_recipes 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_recipes 表的 updated_at 字段已修复';
END
"""))

        # 修复 menu_recipes_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_recipes_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE menu_recipes_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_recipes_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_recipes_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_recipes_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE menu_recipes_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_recipes_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 menu_recipes_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_recipes_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE menu_recipes_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_recipes_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 menu_recipes_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'menu_recipes_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE menu_recipes_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'menu_recipes_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 module_visibility 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'module_visibility' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE module_visibility 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'module_visibility 表的 created_at 字段已修复';
END
"""))

        # 修复 module_visibility 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'module_visibility' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE module_visibility 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'module_visibility 表的 updated_at 字段已修复';
END
"""))

        # 修复 notifications 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'notifications' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE notifications 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'notifications 表的 created_at 字段已修复';
END
"""))

        # 修复 notifications_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'notifications_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE notifications_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'notifications_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 notifications_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'notifications_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE notifications_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'notifications_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 product_batches 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'product_batches' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE product_batches 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'product_batches 表的 created_at 字段已修复';
END
"""))

        # 修复 product_batches 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'product_batches' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE product_batches 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'product_batches 表的 updated_at 字段已修复';
END
"""))

        # 修复 product_spec_parameters 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'product_spec_parameters' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE product_spec_parameters 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'product_spec_parameters 表的 created_at 字段已修复';
END
"""))

        # 修复 purchase_order_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_order_items' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE purchase_order_items 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_order_items 表的 created_at 字段已修复';
END
"""))

        # 修复 purchase_order_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_order_items' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE purchase_order_items 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_order_items 表的 updated_at 字段已修复';
END
"""))

        # 修复 purchase_orders 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_orders' 
    AND COLUMN_NAME = 'order_date'
)
BEGIN
    ALTER TABLE purchase_orders 
    ALTER COLUMN order_date DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_orders 表的 order_date 字段已修复';
END
"""))

        # 修复 purchase_orders 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_orders' 
    AND COLUMN_NAME = 'delivery_date'
)
BEGIN
    ALTER TABLE purchase_orders 
    ALTER COLUMN delivery_date DATETIME2(1) NULL;
    
    PRINT 'purchase_orders 表的 delivery_date 字段已修复';
END
"""))

        # 修复 purchase_orders 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_orders' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE purchase_orders 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_orders 表的 created_at 字段已修复';
END
"""))

        # 修复 purchase_orders 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_orders' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE purchase_orders 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_orders 表的 updated_at 字段已修复';
END
"""))

        # 修复 purchase_requisition_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_requisition_items' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE purchase_requisition_items 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_requisition_items 表的 created_at 字段已修复';
END
"""))

        # 修复 purchase_requisition_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_requisition_items' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE purchase_requisition_items 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_requisition_items 表的 updated_at 字段已修复';
END
"""))

        # 修复 purchase_requisitions 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_requisitions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE purchase_requisitions 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_requisitions 表的 created_at 字段已修复';
END
"""))

        # 修复 purchase_requisitions 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'purchase_requisitions' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE purchase_requisitions 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'purchase_requisitions 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_categories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_categories' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_categories 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'recipe_categories 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_categories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_categories' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipe_categories 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'recipe_categories 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_categories_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_categories_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_categories_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_categories_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_categories_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_categories_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipe_categories_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_categories_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_categories_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_categories_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_categories_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_categories_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_categories_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_categories_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipe_categories_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_categories_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_improvement_suggestions 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_improvement_suggestions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_improvement_suggestions 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_improvement_suggestions 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_process_ingredients 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_process_ingredients' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_process_ingredients 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_process_ingredients 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_process_ingredients 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_process_ingredients' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipe_process_ingredients 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_process_ingredients 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_processes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_processes' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_processes 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_processes 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_processes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_processes' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipe_processes 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_processes 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_review_images 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_review_images' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_review_images 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_review_images 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_reviews 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_reviews' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_reviews 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_reviews 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_reviews 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_reviews' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipe_reviews 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_reviews 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipe_tag_relations 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_tag_relations' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_tag_relations 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_tag_relations 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_tag_relations_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_tag_relations_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_tag_relations_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_tag_relations_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_tag_relations_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_tag_relations_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_tag_relations_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'recipe_tag_relations_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_tags 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_tags' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_tags 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_tags 表的 created_at 字段已修复';
END
"""))

        # 修复 recipe_versions 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipe_versions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipe_versions 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipe_versions 表的 created_at 字段已修复';
END
"""))

        # 修复 recipes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipes' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipes 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'recipes 表的 created_at 字段已修复';
END
"""))

        # 修复 recipes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipes' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipes 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'recipes 表的 updated_at 字段已修复';
END
"""))

        # 修复 recipes_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipes_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE recipes_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipes_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 recipes_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'recipes_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE recipes_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'recipes_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 roles 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'roles' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE roles 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'roles 表的 created_at 字段已修复';
END
"""))

        # 修复 roles_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'roles_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE roles_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'roles_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 roles_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'roles_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE roles_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'roles_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 sanitation_checks 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'sanitation_checks' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE sanitation_checks 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'sanitation_checks 表的 created_at 字段已修复';
END
"""))

        # 修复 sanitation_checks 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'sanitation_checks' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE sanitation_checks 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'sanitation_checks 表的 updated_at 字段已修复';
END
"""))

        # 修复 school_settings 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'school_settings' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE school_settings 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'school_settings 表的 created_at 字段已修复';
END
"""))

        # 修复 school_settings 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'school_settings' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE school_settings 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'school_settings 表的 updated_at 字段已修复';
END
"""))

        # 修复 standard_units 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'standard_units' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE standard_units 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'standard_units 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_in_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_in_items' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE stock_in_items 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_in_items 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_in_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_in_items' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE stock_in_items 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_in_items 表的 updated_at 字段已修复';
END
"""))

        # 修复 stock_in_items_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_in_items_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE stock_in_items_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_in_items_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_in_items_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_in_items_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE stock_in_items_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_in_items_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 stock_in_items_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_in_items_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE stock_in_items_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_in_items_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_in_items_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_in_items_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE stock_in_items_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_in_items_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 stock_ins 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_ins' 
    AND COLUMN_NAME = 'stock_in_date'
)
BEGIN
    ALTER TABLE stock_ins 
    ALTER COLUMN stock_in_date DATETIME2(1) NOT NULL;
    
    PRINT 'stock_ins 表的 stock_in_date 字段已修复';
END
"""))

        # 修复 stock_ins 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_ins' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE stock_ins 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_ins 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_ins 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_ins' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE stock_ins 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_ins 表的 updated_at 字段已修复';
END
"""))

        # 修复 stock_out_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_out_items' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE stock_out_items 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_out_items 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_out_items 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_out_items' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE stock_out_items 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_out_items 表的 updated_at 字段已修复';
END
"""))

        # 修复 stock_outs 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_outs' 
    AND COLUMN_NAME = 'stock_out_date'
)
BEGIN
    ALTER TABLE stock_outs 
    ALTER COLUMN stock_out_date DATETIME2(1) NOT NULL;
    
    PRINT 'stock_outs 表的 stock_out_date 字段已修复';
END
"""))

        # 修复 stock_outs 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_outs' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE stock_outs 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_outs 表的 created_at 字段已修复';
END
"""))

        # 修复 stock_outs 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'stock_outs' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE stock_outs 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'stock_outs 表的 updated_at 字段已修复';
END
"""))

        # 修复 storage_locations 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'storage_locations' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE storage_locations 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'storage_locations 表的 created_at 字段已修复';
END
"""))

        # 修复 storage_locations 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'storage_locations' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE storage_locations 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'storage_locations 表的 updated_at 字段已修复';
END
"""))

        # 修复 storage_locations_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'storage_locations_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE storage_locations_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'storage_locations_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 storage_locations_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'storage_locations_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE storage_locations_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'storage_locations_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_categories 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_categories' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_categories 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_categories 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_categories_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_categories_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_categories_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_categories_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_categories_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_categories_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_categories_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_categories_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_certificates 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_certificates' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_certificates 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'supplier_certificates 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_certificates 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_certificates' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_certificates 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'supplier_certificates 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_certificates_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_certificates_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_certificates_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_certificates_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_certificates_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_certificates_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_certificates_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_certificates_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_certificates_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_certificates_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_certificates_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_certificates_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_certificates_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_certificates_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_certificates_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_certificates_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_deliveries 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_deliveries' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_deliveries 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_deliveries 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_deliveries 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_deliveries' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_deliveries 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_deliveries 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_deliveries_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_deliveries_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_deliveries_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_deliveries_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_deliveries_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_deliveries_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_deliveries_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_deliveries_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_deliveries_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_deliveries_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_deliveries_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_deliveries_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_deliveries_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_deliveries_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_deliveries_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_deliveries_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_products 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_products' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_products 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_products 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_products 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_products' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_products 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_products 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_products 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_products' 
    AND COLUMN_NAME = 'shelf_time'
)
BEGIN
    ALTER TABLE supplier_products 
    ALTER COLUMN shelf_time DATETIME2(1) NULL;
    
    PRINT 'supplier_products 表的 shelf_time 字段已修复';
END
"""))

        # 修复 supplier_school_relations 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_school_relations' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_school_relations 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'supplier_school_relations 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_school_relations 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_school_relations' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_school_relations 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'supplier_school_relations 表的 updated_at 字段已修复';
END
"""))

        # 修复 supplier_school_relations_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_school_relations_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE supplier_school_relations_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_school_relations_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 supplier_school_relations_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'supplier_school_relations_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE supplier_school_relations_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'supplier_school_relations_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 suppliers 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'suppliers' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE suppliers 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'suppliers 表的 created_at 字段已修复';
END
"""))

        # 修复 suppliers 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'suppliers' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE suppliers 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'suppliers 表的 updated_at 字段已修复';
END
"""))

        # 修复 suppliers_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'suppliers_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE suppliers_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'suppliers_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 suppliers_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'suppliers_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE suppliers_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'suppliers_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 suppliers_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'suppliers_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE suppliers_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'suppliers_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 suppliers_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'suppliers_temp_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE suppliers_temp_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'suppliers_temp_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 system_modules 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_modules' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE system_modules 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_modules 表的 created_at 字段已修复';
END
"""))

        # 修复 system_modules 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_modules' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE system_modules 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_modules 表的 updated_at 字段已修复';
END
"""))

        # 修复 system_modules_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_modules_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE system_modules_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_modules_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 system_modules_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_modules_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE system_modules_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_modules_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 system_settings 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_settings' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE system_settings 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_settings 表的 created_at 字段已修复';
END
"""))

        # 修复 system_settings 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_settings' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE system_settings 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_settings 表的 updated_at 字段已修复';
END
"""))

        # 修复 system_settings_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_settings_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE system_settings_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_settings_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 system_settings_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'system_settings_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE system_settings_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'system_settings_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 trace_documents 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'trace_documents' 
    AND COLUMN_NAME = 'upload_time'
)
BEGIN
    ALTER TABLE trace_documents 
    ALTER COLUMN upload_time DATETIME2(1) NOT NULL;
    
    PRINT 'trace_documents 表的 upload_time 字段已修复';
END
"""))

        # 修复 trace_documents 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'trace_documents' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE trace_documents 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'trace_documents 表的 created_at 字段已修复';
END
"""))

        # 修复 training_records 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'training_records' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE training_records 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'training_records 表的 created_at 字段已修复';
END
"""))

        # 修复 user_recipe_favorites 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'user_recipe_favorites' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE user_recipe_favorites 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'user_recipe_favorites 表的 created_at 字段已修复';
END
"""))

        # 修复 user_search_history 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'user_search_history' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE user_search_history 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'user_search_history 表的 created_at 字段已修复';
END
"""))

        # 修复 users 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'last_login'
)
BEGIN
    ALTER TABLE users 
    ALTER COLUMN last_login DATETIME2(1) NULL;
    
    PRINT 'users 表的 last_login 字段已修复';
END
"""))

        # 修复 users 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE users 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'users 表的 created_at 字段已修复';
END
"""))

        # 修复 users_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users_temp' 
    AND COLUMN_NAME = 'last_login'
)
BEGIN
    ALTER TABLE users_temp 
    ALTER COLUMN last_login DATETIME2(1) NULL;
    
    PRINT 'users_temp 表的 last_login 字段已修复';
END
"""))

        # 修复 users_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE users_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'users_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 users_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users_temp_temp' 
    AND COLUMN_NAME = 'last_login'
)
BEGIN
    ALTER TABLE users_temp_temp 
    ALTER COLUMN last_login DATETIME2(1) NULL;
    
    PRINT 'users_temp_temp 表的 last_login 字段已修复';
END
"""))

        # 修复 users_temp_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'users_temp_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE users_temp_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'users_temp_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 warehouses 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'warehouses' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE warehouses 
    ALTER COLUMN created_at DATETIME2(1) NULL;
    
    PRINT 'warehouses 表的 created_at 字段已修复';
END
"""))

        # 修复 warehouses 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'warehouses' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE warehouses 
    ALTER COLUMN updated_at DATETIME2(1) NULL;
    
    PRINT 'warehouses 表的 updated_at 字段已修复';
END
"""))

        # 修复 warehouses_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'warehouses_temp' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE warehouses_temp 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'warehouses_temp 表的 created_at 字段已修复';
END
"""))

        # 修复 warehouses_temp 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'warehouses_temp' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE warehouses_temp 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'warehouses_temp 表的 updated_at 字段已修复';
END
"""))

        # 修复 weekly_menu_recipes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'weekly_menu_recipes' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE weekly_menu_recipes 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'weekly_menu_recipes 表的 created_at 字段已修复';
END
"""))

        # 修复 weekly_menu_recipes 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'weekly_menu_recipes' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE weekly_menu_recipes 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'weekly_menu_recipes 表的 updated_at 字段已修复';
END
"""))

        # 修复 weekly_menus 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'weekly_menus' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE weekly_menus 
    ALTER COLUMN created_at DATETIME2(1) NOT NULL;
    
    PRINT 'weekly_menus 表的 created_at 字段已修复';
END
"""))

        # 修复 weekly_menus 表
        db.session.execute(text("""
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'weekly_menus' 
    AND COLUMN_NAME = 'updated_at'
)
BEGIN
    ALTER TABLE weekly_menus 
    ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
    
    PRINT 'weekly_menus 表的 updated_at 字段已修复';
END
"""))

        # 提交事务
        db.session.commit()
        logger.info("所有 DATETIME2 字段的精度问题已修复")
        return True
    except Exception as e:
        db.session.rollback()
        logger.error(f"修复 DATETIME2 字段精度时出错: {str(e)}")
        return False

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        if fix_datetime_precision():
            print("成功修复所有 DATETIME2 字段的精度问题")
        else:
            print("修复 DATETIME2 字段精度时出错，请查看日志")
