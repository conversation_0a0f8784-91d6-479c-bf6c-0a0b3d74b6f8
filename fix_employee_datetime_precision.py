"""
修复员工管理模块 DATETIME2 字段的精度问题

此脚本使用系统自带的框架修复员工管理模块相关表中 DATETIME2 字段的精度问题。
涉及的表包括：
- employees
- health_certificates
- medical_examinations
- daily_health_checks
- training_records
"""
from app import db, create_app
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_employee_datetime_precision():
    """修复员工管理模块相关表的 DATETIME2 字段精度问题"""
    try:
        # 使用系统自带的会话执行SQL
        # 修复 employees 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'employees' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE employees 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            ALTER TABLE employees 
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
            
            PRINT 'employees 表的 created_at 和 updated_at 字段已修复';
        END
        """))
        
        # 修复 health_certificates 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'health_certificates' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE health_certificates 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            IF EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'health_certificates' 
                AND COLUMN_NAME = 'updated_at'
            )
            BEGIN
                ALTER TABLE health_certificates 
                ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
                
                PRINT 'health_certificates 表的 updated_at 字段已修复';
            END
            
            PRINT 'health_certificates 表的 created_at 字段已修复';
        END
        """))
        
        # 修复 medical_examinations 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'medical_examinations' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE medical_examinations 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            PRINT 'medical_examinations 表的 created_at 字段已修复';
        END
        """))
        
        # 修复 daily_health_checks 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'daily_health_checks' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE daily_health_checks 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            PRINT 'daily_health_checks 表的 created_at 字段已修复';
        END
        """))
        
        # 修复 training_records 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'training_records' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE training_records 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            PRINT 'training_records 表的 created_at 字段已修复';
        END
        """))
        
        # 提交事务
        db.session.commit()
        logger.info("员工管理模块所有 DATETIME2 字段的精度问题已修复")
        return True
    except Exception as e:
        db.session.rollback()
        logger.error(f"修复员工管理模块 DATETIME2 字段精度时出错: {str(e)}")
        return False

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        if fix_employee_datetime_precision():
            print("成功修复员工管理模块 DATETIME2 字段的精度问题")
        else:
            print("修复员工管理模块 DATETIME2 字段精度时出错，请查看日志")
