"""
综合修复DATETIME2精度问题

此脚本提供了一个综合解决方案，包括：
1. 修复数据库表结构
2. 应用模型补丁
3. 应用SQLAlchemy事件监听器
4. 创建辅助函数
"""
import os
import sys
import logging
from datetime import datetime

# 配置日志
log_file = f"datetime_fix_comprehensive_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def ensure_directory(path):
    """确保目录存在"""
    directory = os.path.dirname(path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)

def fix_database_structure():
    """修复数据库表结构"""
    try:
        # 导入并运行数据库修复脚本
        from fix_all_datetime_fields import main as fix_db
        logger.info("开始修复数据库表结构...")
        fix_db()
        logger.info("数据库表结构修复完成")
        return True
    except Exception as e:
        logger.error(f"修复数据库表结构时出错: {e}")
        return False

def apply_model_patch():
    """应用模型补丁"""
    try:
        # 导入并运行模型修复脚本
        from fix_datetime_precision_model import main as fix_model
        logger.info("开始应用模型补丁...")
        fix_model()
        logger.info("模型补丁应用完成")
        return True
    except Exception as e:
        logger.error(f"应用模型补丁时出错: {e}")
        return False

def create_datetime_patch():
    """创建datetime补丁"""
    try:
        # 确保目录存在
        ensure_directory("app/utils/datetime_patch.py")
        
        # 创建datetime补丁
        with open("app/utils/datetime_patch.py", "w", encoding="utf-8") as f:
            f.write("""\"\"\"
全局修补 datetime.now 函数

此模块在导入时自动修补 datetime.now 函数，
确保它返回的 datetime 对象的微秒部分为 0。
这样就不需要修改任何使用 datetime.now() 的代码。
\"\"\"
import datetime as dt
import logging

# 保存原始的 now 函数
original_now = dt.datetime.now

# 创建安全的 now 函数
def safe_now(tz=None):
    \"\"\"安全的 now 函数，确保返回的 datetime 对象的微秒部分为 0\"\"\"
    return original_now(tz).replace(microsecond=0)

# 全局替换 datetime.now 函数
dt.datetime.now = safe_now

# 记录日志
logging.getLogger(__name__).info("已全局修补 datetime.now 函数")

# 导出安全的 datetime 函数
def safe_datetime(dt_obj=None):
    \"\"\"
    创建一个安全的 datetime 对象，确保微秒部分为 0
    
    Args:
        dt_obj: 要处理的 datetime 对象，如果为 None 则使用当前时间
        
    Returns:
        处理后的 datetime 对象，确保微秒部分为 0
    \"\"\"
    if dt_obj is None:
        dt_obj = dt.datetime.now()
    return dt_obj.replace(microsecond=0)
""")
        logger.info("已创建datetime补丁")
        return True
    except Exception as e:
        logger.error(f"创建datetime补丁时出错: {e}")
        return False

def create_sqlalchemy_patch():
    """创建SQLAlchemy补丁"""
    try:
        # 确保目录存在
        ensure_directory("app/utils/sqlalchemy_patch.py")
        
        # 创建SQLAlchemy补丁
        with open("app/utils/sqlalchemy_patch.py", "w", encoding="utf-8") as f:
            f.write("""\"\"\"
SQLAlchemy补丁

为SQLAlchemy添加事件监听器，在插入或更新数据前自动处理datetime字段。
\"\"\"
from datetime import datetime
from sqlalchemy import event, inspect
from sqlalchemy.ext.declarative import DeclarativeMeta
import logging

logger = logging.getLogger(__name__)

def safe_datetime(dt=None):
    \"\"\"创建安全的datetime对象\"\"\"
    if dt is None:
        dt = datetime.now()
    return dt.replace(microsecond=0)

def process_datetime_attributes(mapper, connection, target):
    \"\"\"处理对象的datetime属性\"\"\"
    for column_name, column in inspect(target.__class__).columns.items():
        # 检查列类型是否为datetime
        if hasattr(column.type, 'python_type') and column.type.python_type is datetime:
            # 获取属性值
            value = getattr(target, column_name, None)
            # 如果值是datetime对象，确保微秒部分为0
            if isinstance(value, datetime):
                setattr(target, column_name, value.replace(microsecond=0))

def apply_patch(db):
    \"\"\"应用SQLAlchemy补丁\"\"\"
    try:
        # 为所有模型添加事件监听器
        for model in db.Model._decl_class_registry.values():
            if isinstance(model, DeclarativeMeta):
                # 在插入前处理datetime属性
                event.listen(model, 'before_insert', process_datetime_attributes)
                # 在更新前处理datetime属性
                event.listen(model, 'before_update', process_datetime_attributes)
        
        logger.info("已应用SQLAlchemy datetime补丁")
        return True
    except Exception as e:
        logger.error(f"应用SQLAlchemy datetime补丁时出错: {e}")
        return False
""")
        logger.info("已创建SQLAlchemy补丁")
        return True
    except Exception as e:
        logger.error(f"创建SQLAlchemy补丁时出错: {e}")
        return False

def update_app_init():
    """更新app/__init__.py文件，应用补丁"""
    try:
        # 检查文件是否存在
        if not os.path.exists("app/__init__.py"):
            logger.warning("app/__init__.py文件不存在，跳过更新")
            return False
        
        # 读取文件内容
        with open("app/__init__.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 备份原文件
        backup_file = f"app/__init__.py.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        with open(backup_file, "w", encoding="utf-8") as f:
            f.write(content)
        logger.info(f"已备份原文件: {backup_file}")
        
        # 检查是否已经应用了补丁
        if "from app.utils import datetime_patch" in content or "from app.utils.sqlalchemy_patch import apply_patch" in content:
            logger.info("app/__init__.py文件已包含补丁代码，跳过更新")
            return True
        
        # 查找create_app函数
        if "def create_app(" in content:
            # 在create_app函数开头添加导入语句
            import_code = """
    # 导入datetime补丁，全局修补datetime.now函数
    from app.utils import datetime_patch
"""
            # 在create_app函数末尾添加SQLAlchemy补丁应用代码
            patch_code = """
    # 应用SQLAlchemy补丁
    from app.utils.sqlalchemy_patch import apply_patch
    apply_patch(db)
"""
            # 在db初始化后添加补丁应用代码
            if "db.init_app(app)" in content:
                content = content.replace("db.init_app(app)", "db.init_app(app)" + patch_code)
            else:
                # 如果找不到db.init_app(app)，在return app前添加
                content = content.replace("return app", patch_code + "\n    return app")
            
            # 在create_app函数开头添加导入语句
            content = content.replace("def create_app(", import_code + "def create_app(")
            
            # 写入修改后的内容
            with open("app/__init__.py", "w", encoding="utf-8") as f:
                f.write(content)
            
            logger.info("已更新app/__init__.py文件，应用补丁")
            return True
        else:
            logger.warning("在app/__init__.py中未找到create_app函数，跳过更新")
            return False
    except Exception as e:
        logger.error(f"更新app/__init__.py文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "综合修复DATETIME2精度问题")
    print("="*80)
    print(f"\n日志文件: {log_file}")
    
    # 1. 修复数据库表结构
    print("\n1. 修复数据库表结构")
    if fix_database_structure():
        print("  ✓ 数据库表结构修复成功")
    else:
        print("  ✗ 数据库表结构修复失败，请查看日志")
    
    # 2. 创建补丁文件
    print("\n2. 创建补丁文件")
    if create_datetime_patch():
        print("  ✓ datetime补丁创建成功")
    else:
        print("  ✗ datetime补丁创建失败，请查看日志")
    
    if create_sqlalchemy_patch():
        print("  ✓ SQLAlchemy补丁创建成功")
    else:
        print("  ✗ SQLAlchemy补丁创建失败，请查看日志")
    
    # 3. 更新app/__init__.py文件
    print("\n3. 更新app/__init__.py文件")
    if update_app_init():
        print("  ✓ app/__init__.py文件更新成功")
    else:
        print("  ✗ app/__init__.py文件更新失败，请查看日志")
    
    # 4. 应用模型补丁
    print("\n4. 应用模型补丁")
    if apply_model_patch():
        print("  ✓ 模型补丁应用成功")
    else:
        print("  ✗ 模型补丁应用失败，请查看日志")
    
    print("\n修复完成！请重启应用以应用所有补丁。")
    print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
