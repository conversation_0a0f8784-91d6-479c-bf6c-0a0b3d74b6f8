"""
执行修复datetime字段精度的SQL脚本
"""
import os
import sys
import pyodbc
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# 创建一个简单的Flask应用
app = Flask(__name__)

# 从环境变量或配置文件获取数据库连接信息
# 这里假设使用的是与应用相同的数据库配置
try:
    # 尝试从app配置中导入
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
    from app.config import Config
    app.config.from_object(Config)
except ImportError:
    # 如果导入失败，使用默认配置
    print("无法导入应用配置，使用默认数据库配置")
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mssql+pyodbc:///?odbc_connect=DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost;DATABASE=StudentsCMSSP;Trusted_Connection=yes'

db = SQLAlchemy(app)

def run_sql_script():
    """执行SQL脚本文件"""
    # 获取SQL脚本文件路径
    script_path = os.path.join(os.path.dirname(__file__), 'fix_datetime_precision.sql')
    
    if not os.path.exists(script_path):
        print(f"错误: SQL脚本文件不存在: {script_path}")
        return False
    
    # 读取SQL脚本内容
    with open(script_path, 'r', encoding='utf-8') as f:
        sql_script = f.read()
    
    # 从SQLAlchemy连接中获取原始连接
    try:
        with app.app_context():
            connection = db.engine.raw_connection()
            cursor = connection.cursor()
            
            # 分割SQL脚本为单独的语句并执行
            # 这里简单地按GO关键字分割，如果脚本中没有GO，就作为一个整体执行
            statements = sql_script.split('GO')
            if len(statements) == 1:
                print("执行完整SQL脚本...")
                try:
                    cursor.execute(sql_script)
                    print("SQL脚本执行成功")
                except Exception as e:
                    print(f"执行SQL脚本时出错: {str(e)}")
                    return False
            else:
                print(f"执行SQL脚本 (共{len(statements)}个语句)...")
                for i, statement in enumerate(statements):
                    if statement.strip():
                        try:
                            cursor.execute(statement)
                            print(f"语句 {i+1}/{len(statements)} 执行成功")
                        except Exception as e:
                            print(f"执行语句 {i+1}/{len(statements)} 时出错: {str(e)}")
                            # 继续执行其他语句
            
            # 提交事务
            connection.commit()
            print("所有更改已提交到数据库")
            
            # 关闭连接
            cursor.close()
            connection.close()
            
            return True
    except Exception as e:
        print(f"连接数据库时出错: {str(e)}")
        return False

if __name__ == '__main__':
    print("开始执行修复datetime字段精度的SQL脚本...")
    success = run_sql_script()
    if success:
        print("脚本执行完成，所有datetime字段的精度应已修复")
    else:
        print("脚本执行失败，请检查错误信息")
