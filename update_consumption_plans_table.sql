-- 添加消耗计划表的新字段
ALTER TABLE consumption_plans ADD consumption_date DATE NULL;
ALTER TABLE consumption_plans ADD meal_type NVARCHAR(20) NULL;
ALTER TABLE consumption_plans ADD diners_count INT NULL;

-- 更新menu_plan_id字段为可空
ALTER TABLE consumption_plans ALTER COLUMN menu_plan_id INT NULL;

-- 为已有记录设置默认值
UPDATE consumption_plans SET 
    consumption_date = (SELECT plan_date FROM menu_plans WHERE menu_plans.id = consumption_plans.menu_plan_id),
    meal_type = (SELECT meal_type FROM menu_plans WHERE menu_plans.id = consumption_plans.menu_plan_id),
    diners_count = (SELECT expected_diners FROM menu_plans WHERE menu_plans.id = consumption_plans.menu_plan_id)
WHERE menu_plan_id IS NOT NULL;
