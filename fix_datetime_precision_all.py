"""
全面修复datetime精度问题

此脚本将：
1. 修改所有模型中的db.DateTime为DATETIME2(precision=1)
2. 生成一个SQL脚本来修改数据库中所有datetime2(0)类型的列为datetime2(1)
"""

import os
import re
import sys
import time
from datetime import datetime

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 模型文件列表
MODEL_FILES = [
    "app/models.py",
    "app/models_phase1.py",
    "app/models_phase2.py",
    "app/models_phase3.py",
    "app/models_phase4.py",
    "app/models_recipe_advanced.py",
    "app/models_ingredient_traceability.py",
    "app/models_supplier.py",
    "app/models/notification.py"
]

def backup_file(file_path):
    """备份文件"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
        try:
            with open(file_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            print(f"已备份文件: {file_path} -> {backup_path}")
            return True
        except Exception as e:
            print(f"备份文件时出错: {str(e)}")
            return False
    return False

def fix_datetime_in_file(file_path, precision=1):
    """修改文件中的DateTime字段定义"""
    full_path = os.path.join(PROJECT_ROOT, file_path)

    # 检查文件是否存在
    if not os.path.exists(full_path):
        print(f"文件不存在: {full_path}")
        return 0

    # 备份文件
    if not backup_file(full_path):
        print(f"无法备份文件，跳过修改: {full_path}")
        return 0

    try:
        # 读取文件内容
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 查找所有的DateTime字段定义
        # 模式: db.Column(db.DateTime, ...)
        pattern = r'db\.Column\(db\.DateTime(\s*,|\s*\()'

        # 替换为带精度的版本
        # db.Column(db.DateTime, ...) -> db.Column(DATETIME2(precision=1), ...)
        # db.Column(db.DateTime(...), ...) -> db.Column(db.DateTime(...), ...)
        def replace_datetime(match):
            if match.group(1).strip() == '(':
                # 已经有括号，不需要修改
                return match.group(0)
            else:
                # 没有括号，添加精度参数
                return f'db.Column(db.DateTime({precision})'

        content = re.sub(pattern, replace_datetime, content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改文件: {full_path}")
            return 1
        else:
            print(f"文件无需修改: {full_path}")
            return 0

    except Exception as e:
        print(f"修改文件时出错: {str(e)}")
        return 0

def generate_sql_script(output_file="fix_datetime_precision.sql"):
    """生成SQL脚本来修改数据库中的datetime2字段精度"""
    sql_script = """-- 修复日期时间精度问题
-- 这个脚本将修改所有表中的datetime2字段，将其精度设置为1

-- 获取所有表中的datetime2字段
DECLARE @sql NVARCHAR(MAX) = '';

-- 构建修改语句
SELECT @sql = @sql + 
    'IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''[dbo].[' + t.name + ']'') AND name = ''' + c.name + ''' AND system_type_id = 42) ' +
    'BEGIN ' +
    'PRINT ''修改表 ' + t.name + ' 中的字段 ' + c.name + ' 的精度为1''; ' +
    'ALTER TABLE [dbo].[' + t.name + '] ALTER COLUMN [' + c.name + '] DATETIME2(1) ' + 
    CASE WHEN c.is_nullable = 0 THEN 'NOT NULL' ELSE 'NULL' END + '; ' +
    'END; '
FROM sys.tables t
JOIN sys.columns c ON t.object_id = c.object_id
JOIN sys.types ty ON c.system_type_id = ty.system_type_id
WHERE t.is_ms_shipped = 0 -- 非系统表
AND ty.name = 'datetime2'
AND c.max_length = 8; -- datetime2(0)的长度是8字节

-- 执行修改语句
PRINT '开始修复datetime2字段精度问题...';
EXEC sp_executesql @sql;
PRINT '修复完成';
"""

    # 写入SQL脚本文件
    output_path = os.path.join(PROJECT_ROOT, output_file)
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(sql_script)
        print(f"已生成SQL脚本: {output_path}")
        return True
    except Exception as e:
        print(f"生成SQL脚本时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始全面修复datetime精度问题...")

    # 步骤1: 修改所有模型文件中的DateTime字段定义
    print("\n步骤1: 修改所有模型文件中的DateTime字段定义")
    modified_count = 0
    for model_file in MODEL_FILES:
        if os.path.exists(os.path.join(PROJECT_ROOT, model_file)):
            modified_count += fix_datetime_in_file(model_file)
    print(f"共修改了 {modified_count} 个模型文件")

    # 步骤2: 生成SQL脚本来修改数据库中的datetime2字段精度
    print("\n步骤2: 生成SQL脚本来修改数据库中的datetime2字段精度")
    if generate_sql_script():
        print("SQL脚本生成成功，请在SQL Server Management Studio中执行该脚本")
    else:
        print("SQL脚本生成失败")

    print("\n全面修复datetime精度问题完成")
    print("请重启应用程序以应用更改")

if __name__ == "__main__":
    main()
