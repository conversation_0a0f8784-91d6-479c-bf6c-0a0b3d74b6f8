@echo off
echo 正在创建虚拟环境...

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python！
    echo 请先安装Python 3.8或更高版本。
    pause
    exit /b 1
)

:: 检查虚拟环境是否已存在
if exist "venv" (
    echo 虚拟环境已存在，是否重新创建？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在删除旧的虚拟环境...
        rmdir /s /q venv
    ) else (
        echo 操作已取消。
        pause
        exit /b 0
    )
)

:: 创建虚拟环境
echo 正在创建新的虚拟环境...
python -m venv venv

:: 激活虚拟环境
call venv\Scripts\activate.bat

:: 升级pip
python -m pip install --upgrade pip

:: 安装依赖
if exist "requirements.txt" (
    echo 正在安装依赖...
    pip install -r requirements.txt
) else (
    echo 警告：requirements.txt 文件不存在，跳过依赖安装。
)

echo 虚拟环境创建完成！
echo 现在可以运行 start.bat 启动项目了。
pause 