-- 检查 inspection_records 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'inspection_records')
BEGIN
    -- 创建 inspection_records 表
    CREATE TABLE inspection_records (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        inspection_type VARCHAR(10) NOT NULL,
        inspection_item VARCHAR(100) NOT NULL,
        status VARCHAR(10) DEFAULT 'normal',
        description NVARCHAR(MAX),
        inspector_id INT,
        inspection_time DATETIME2(1) DEFAULT GETDATE(),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_inspection_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id),
        CONSTRAINT FK_inspection_inspector FOREIGN KEY (inspector_id) REFERENCES users(id),
        CONSTRAINT CK_inspection_type CHECK (inspection_type IN ('morning', 'noon', 'evening')),
        CONSTRAINT CK_inspection_status CHECK (status IN ('normal', 'abnormal'))
    );
    
    PRINT 'inspection_records 表已创建';
END
ELSE
BEGIN
    PRINT 'inspection_records 表已存在';
    
    -- 检查列是否存在，如果不存在则添加
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'daily_log_id')
    BEGIN
        ALTER TABLE inspection_records ADD daily_log_id INT NOT NULL;
        PRINT '添加了 daily_log_id 列';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'inspection_type')
    BEGIN
        ALTER TABLE inspection_records ADD inspection_type VARCHAR(10) NOT NULL;
        PRINT '添加了 inspection_type 列';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'inspection_item')
    BEGIN
        ALTER TABLE inspection_records ADD inspection_item VARCHAR(100) NOT NULL;
        PRINT '添加了 inspection_item 列';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'status')
    BEGIN
        ALTER TABLE inspection_records ADD status VARCHAR(10) DEFAULT 'normal';
        PRINT '添加了 status 列';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'description')
    BEGIN
        ALTER TABLE inspection_records ADD description NVARCHAR(MAX);
        PRINT '添加了 description 列';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'inspector_id')
    BEGIN
        ALTER TABLE inspection_records ADD inspector_id INT;
        PRINT '添加了 inspector_id 列';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('inspection_records') AND name = 'inspection_time')
    BEGIN
        ALTER TABLE inspection_records ADD inspection_time DATETIME2(1) DEFAULT GETDATE();
        PRINT '添加了 inspection_time 列';
    END
END

-- 检查 dining_companions 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'dining_companions')
BEGIN
    -- 创建 dining_companions 表
    CREATE TABLE dining_companions (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        companion_name VARCHAR(100) NOT NULL,
        companion_role VARCHAR(100) NOT NULL,
        meal_type VARCHAR(10) NOT NULL,
        dining_time DATETIME2(1) NOT NULL,
        taste_rating INT,
        hygiene_rating INT,
        service_rating INT,
        comments NVARCHAR(MAX),
        suggestions NVARCHAR(MAX),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_companion_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id),
        CONSTRAINT CK_meal_type CHECK (meal_type IN ('breakfast', 'lunch', 'dinner'))
    );
    
    PRINT 'dining_companions 表已创建';
END
ELSE
BEGIN
    PRINT 'dining_companions 表已存在';
END

-- 检查 photos 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'photos')
BEGIN
    -- 创建 photos 表
    CREATE TABLE photos (
        id INT PRIMARY KEY IDENTITY(1,1),
        reference_id INT NOT NULL,
        reference_type VARCHAR(20) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(255) NOT NULL,
        description VARCHAR(255),
        upload_time DATETIME2(1) DEFAULT GETDATE(),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT CK_reference_type CHECK (reference_type IN ('inspection', 'companion', 'training', 'event', 'issue'))
    );
    
    -- 创建索引
    CREATE INDEX idx_photos_reference ON photos (reference_type, reference_id);
    
    PRINT 'photos 表已创建';
END
ELSE
BEGIN
    PRINT 'photos 表已存在';
END
