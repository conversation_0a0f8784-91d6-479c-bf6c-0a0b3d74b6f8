-- 检查 dining_companions 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'dining_companions')
BEGIN
    -- 创建 dining_companions 表
    CREATE TABLE dining_companions (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        companion_name VARCHAR(100) NOT NULL,
        companion_role VARCHAR(100) NOT NULL,
        meal_type VARCHAR(10) NOT NULL,
        dining_time DATETIME2(1) NOT NULL,
        taste_rating INT,
        hygiene_rating INT,
        service_rating INT,
        comments NVARCHAR(MAX),
        suggestions NVARCHAR(MAX),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_companion_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id),
        CONSTRAINT CK_meal_type CHECK (meal_type IN ('breakfast', 'lunch', 'dinner'))
    );
    
    PRINT 'dining_companions 表已创建';
END
ELSE
BEGIN
    PRINT 'dining_companions 表已存在';
END

-- 检查 canteen_training_records 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'canteen_training_records')
BEGIN
    -- 创建 canteen_training_records 表
    CREATE TABLE canteen_training_records (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        training_topic VARCHAR(200) NOT NULL,
        trainer VARCHAR(100) NOT NULL,
        training_time DATETIME2(1) NOT NULL,
        location VARCHAR(100),
        duration INT,
        attendees_count INT,
        content_summary NVARCHAR(MAX),
        effectiveness_evaluation NVARCHAR(MAX),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_training_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id)
    );
    
    PRINT 'canteen_training_records 表已创建';
END
ELSE
BEGIN
    PRINT 'canteen_training_records 表已存在';
END

-- 检查 special_events 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'special_events')
BEGIN
    -- 创建 special_events 表
    CREATE TABLE special_events (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        event_type VARCHAR(100) NOT NULL,
        event_time DATETIME2(1) NOT NULL,
        description NVARCHAR(MAX) NOT NULL,
        participants NVARCHAR(MAX),
        handling_measures NVARCHAR(MAX),
        event_summary NVARCHAR(MAX),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_event_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id)
    );
    
    PRINT 'special_events 表已创建';
END
ELSE
BEGIN
    PRINT 'special_events 表已存在';
END

-- 检查 issues 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'issues')
BEGIN
    -- 创建 issues 表
    CREATE TABLE issues (
        id INT PRIMARY KEY IDENTITY(1,1),
        daily_log_id INT NOT NULL,
        issue_type VARCHAR(100) NOT NULL,
        description NVARCHAR(MAX) NOT NULL,
        status VARCHAR(10) DEFAULT 'pending',
        found_time DATETIME2(1) NOT NULL,
        fixed_time DATETIME2(1),
        responsible_person VARCHAR(100),
        verification_result NVARCHAR(MAX),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT FK_issue_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id),
        CONSTRAINT CK_issue_status CHECK (status IN ('pending', 'fixing', 'fixed'))
    );
    
    PRINT 'issues 表已创建';
END
ELSE
BEGIN
    PRINT 'issues 表已存在';
END

-- 检查 photos 表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'photos')
BEGIN
    -- 创建 photos 表
    CREATE TABLE photos (
        id INT PRIMARY KEY IDENTITY(1,1),
        reference_id INT NOT NULL,
        reference_type VARCHAR(20) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(255) NOT NULL,
        description VARCHAR(255),
        upload_time DATETIME2(1) DEFAULT GETDATE(),
        created_at DATETIME2(1) DEFAULT GETDATE(),
        updated_at DATETIME2(1) DEFAULT GETDATE(),
        CONSTRAINT CK_reference_type CHECK (reference_type IN ('inspection', 'companion', 'training', 'event', 'issue'))
    );
    
    -- 创建索引
    CREATE INDEX idx_photos_reference ON photos (reference_type, reference_id);
    
    PRINT 'photos 表已创建';
END
ELSE
BEGIN
    PRINT 'photos 表已存在';
END
