{% extends 'base_public.html' %}

{% block title %}评价{{ inspection_type_name }}照片{% endblock %}

{% block styles %}
<style>
    body {
        background-color: #f8f9fc;
    }
    
    .header-banner {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .school-logo {
        max-height: 80px;
        margin-right: 15px;
    }
    
    .platform-name {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .school-name {
        font-size: 1.2rem;
    }
    
    .photo-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        background-color: white;
    }
    
    .photo-card .card-header {
        background-color: #4e73df;
        color: white;
        font-weight: bold;
        padding: 15px;
    }
    
    .photo-card .card-body {
        padding: 20px;
    }
    
    .photo-container {
        position: relative;
        margin-bottom: 15px;
    }
    
    .inspection-photo {
        width: 100%;
        border-radius: 5px;
        cursor: pointer;
    }
    
    .photo-description {
        margin-top: 10px;
        font-style: italic;
        color: #5a5c69;
    }
    
    .rating-container {
        margin-top: 15px;
    }
    
    .star-rating {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
    }
    
    .star-rating .star {
        font-size: 2rem;
        color: #dddfeb;
        cursor: pointer;
        margin: 0 5px;
        transition: color 0.2s;
    }
    
    .star-rating .star.active {
        color: #f6c23e;
    }
    
    .rating-label {
        text-align: center;
        font-weight: bold;
        color: #5a5c69;
    }
    
    .submit-rating {
        background-color: #4e73df;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: bold;
        margin-top: 15px;
        width: 100%;
    }
    
    .submit-rating:hover {
        background-color: #2e59d9;
    }
    
    .success-message {
        display: none;
        background-color: #1cc88a;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }
    
    .error-message {
        display: none;
        background-color: #e74a3b;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-top: 15px;
        text-align: center;
        font-weight: bold;
    }
    
    .modal-photo {
        max-width: 100%;
        max-height: 80vh;
    }
    
    .no-photos {
        text-align: center;
        padding: 50px 0;
        color: #5a5c69;
    }
    
    .footer {
        background-color: #f8f9fc;
        padding: 20px 0;
        margin-top: 30px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="header-banner">
    <div class="container">
        <div class="d-flex align-items-center">
            <img src="/static/img/logo.png" alt="Logo" class="school-logo">
            <div>
                <div class="platform-name">校园餐智慧食堂监管平台</div>
                <div class="school-name">{{ school.name }}</div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-12 mb-4">
            <h2 class="text-center">{{ school.name }} - {{ log.log_date.strftime('%Y-%m-%d') }} {{ inspection_type_name }}照片评价</h2>
            <p class="text-center text-muted">请为以下检查照片进行评分，您的评价将帮助我们改进食堂管理</p>
        </div>
    </div>
    
    {% if photos_by_item %}
        {% for item, photos in photos_by_item.items() %}
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="photo-card">
                        <div class="card-header">
                            <i class="fas fa-clipboard-check mr-2"></i> {{ item }}
                        </div>
                        <div class="card-body">
                            {% for photo in photos %}
                                <div class="photo-container" id="photo-container-{{ photo.id }}">
                                    <img src="{{ photo.file_path }}" class="inspection-photo" 
                                         alt="{{ item }}" data-toggle="modal" data-target="#photoModal" 
                                         data-photo-src="{{ photo.file_path }}" data-photo-item="{{ item }}">
                                    
                                    {% if photo.description %}
                                        <div class="photo-description">{{ photo.description }}</div>
                                    {% endif %}
                                    
                                    <div class="rating-container">
                                        <div class="star-rating" data-photo-id="{{ photo.id }}">
                                            {% for i in range(1, 6) %}
                                                <i class="fas fa-star star {% if photo.rating >= i %}active{% endif %}" 
                                                   data-rating="{{ i }}"></i>
                                            {% endfor %}
                                        </div>
                                        <div class="rating-label" id="rating-label-{{ photo.id }}">
                                            {% if photo.rating %}
                                                当前评分: {{ photo.rating }} 星
                                            {% else %}
                                                请评分
                                            {% endif %}
                                        </div>
                                        <button class="btn submit-rating" data-photo-id="{{ photo.id }}">
                                            提交评分
                                        </button>
                                        <div class="success-message" id="success-message-{{ photo.id }}">
                                            <i class="fas fa-check-circle mr-2"></i> 评分提交成功！
                                        </div>
                                        <div class="error-message" id="error-message-{{ photo.id }}"></div>
                                    </div>
                                </div>
                                {% if not loop.last %}<hr>{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="photo-card">
                    <div class="card-body">
                        <div class="no-photos">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <h4>暂无照片</h4>
                            <p>当前检查记录暂无照片可供评价</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 照片查看模态框 -->
<div class="modal fade" id="photoModal" tabindex="-1" role="dialog" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">检查照片</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="" class="modal-photo" id="modalPhoto">
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>© {{ now.year }} 校园餐智慧食堂监管平台 - 版权所有</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 照片模态框
    $('#photoModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const photoSrc = button.data('photo-src');
        const photoItem = button.data('photo-item');
        
        const modal = $(this);
        modal.find('.modal-title').text(photoItem + ' - 照片查看');
        modal.find('#modalPhoto').attr('src', photoSrc);
    });
    
    // 星级评分
    $('.star').on('click', function() {
        const rating = $(this).data('rating');
        const starRating = $(this).parent();
        const photoId = starRating.data('photo-id');
        
        // 更新星星显示
        starRating.find('.star').removeClass('active');
        starRating.find('.star').each(function(index) {
            if (index < rating) {
                $(this).addClass('active');
            }
        });
        
        // 更新评分标签
        $(`#rating-label-${photoId}`).text(`当前评分: ${rating} 星`);
    });
    
    // 鼠标悬停效果
    $('.star').on('mouseenter', function() {
        const rating = $(this).data('rating');
        const starRating = $(this).parent();
        
        starRating.find('.star').each(function(index) {
            if (index < rating) {
                $(this).addClass('hover');
            }
        });
    }).on('mouseleave', function() {
        $(this).parent().find('.star').removeClass('hover');
    });
    
    // 提交评分
    $('.submit-rating').on('click', function() {
        const photoId = $(this).data('photo-id');
        const rating = $(this).parent().find('.star.active').length;
        
        if (rating === 0) {
            $(`#error-message-${photoId}`).text('请选择评分');
            $(`#error-message-${photoId}`).show();
            return;
        }
        
        // 隐藏错误信息
        $(`#error-message-${photoId}`).hide();
        
        // 发送评分请求
        fetch('/api/v2/photos/public/rate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                photo_id: photoId,
                rating: rating
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $(`#success-message-${photoId}`).show();
                
                // 3秒后隐藏成功信息
                setTimeout(() => {
                    $(`#success-message-${photoId}`).hide();
                }, 3000);
            } else {
                $(`#error-message-${photoId}`).text(data.error || '评分提交失败，请重试');
                $(`#error-message-${photoId}`).show();
            }
        })
        .catch(error => {
            $(`#error-message-${photoId}`).text('评分提交失败，请重试');
            $(`#error-message-${photoId}`).show();
            console.error('Error:', error);
        });
    });
</script>
{% endblock %}
