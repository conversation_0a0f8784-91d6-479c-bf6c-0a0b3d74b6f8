#!/usr/bin/env python
"""
CSRF令牌使用扫描工具

此脚本扫描项目中的所有HTML模板和JavaScript文件，检查是否正确使用了CSRF令牌。
它会报告可能缺少CSRF保护的表单和AJAX请求。
"""

import os
import re
import sys

# 定义颜色代码（不依赖colorama）
class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    RESET = '\033[0m'

# 定义要扫描的目录
TEMPLATES_DIR = 'app/templates'
STATIC_JS_DIR = 'app/static/js'
PYTHON_DIR = 'app'

# 定义正则表达式模式
FORM_PATTERN = re.compile(r'<form[^>]*method=["\']post["\'][^>]*>', re.IGNORECASE)
CSRF_TOKEN_PATTERN = re.compile(r'(csrf_token\(\)|form\.hidden_tag\(\)|form\.csrf_token|name=["\']csrf_token["\'])', re.IGNORECASE)
AJAX_POST_PATTERN = re.compile(r'(\.ajax\s*\(\s*\{[^}]*type\s*:\s*["\']POST["\']|\.post\s*\()', re.IGNORECASE)
AJAX_CSRF_PATTERN = re.compile(r'(headers\s*:\s*\{[^}]*["\']X-CSRFToken["\']|beforeSend\s*:|\.ajaxSetup\s*\(\s*\{)', re.IGNORECASE)
# 使用简单的模式匹配
CUSTOM_CSRF_PATTERN = re.compile(r'(csrf_token|csrf_secret|csrf_field|csrf_protect)', re.IGNORECASE)

def scan_html_files():
    """扫描HTML文件中的表单，检查是否包含CSRF令牌"""
    print(f"{Colors.BLUE}正在扫描HTML模板文件...{Colors.RESET}")

    forms_without_csrf = []

    for root, dirs, files in os.walk(TEMPLATES_DIR):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 查找所有表单
                form_matches = FORM_PATTERN.findall(content)

                if form_matches:
                    # 检查是否包含CSRF令牌
                    has_csrf = CSRF_TOKEN_PATTERN.search(content) is not None

                    if not has_csrf:
                        forms_without_csrf.append((relative_path, len(form_matches)))

    if forms_without_csrf:
        print(f"{Colors.RED}发现 {len(forms_without_csrf)} 个可能缺少CSRF令牌的HTML文件：{Colors.RESET}")
        for path, count in forms_without_csrf:
            print(f"  - {path} (包含 {count} 个表单)")
    else:
        print(f"{Colors.GREEN}所有HTML表单都包含CSRF令牌。{Colors.RESET}")

    return forms_without_csrf

def scan_js_files():
    """扫描JavaScript文件中的AJAX请求，检查是否正确处理CSRF令牌"""
    print(f"\n{Colors.BLUE}正在扫描JavaScript文件...{Colors.RESET}")

    ajax_without_csrf = []

    for root, dirs, files in os.walk(STATIC_JS_DIR):
        for file in files:
            if file.endswith('.js'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 查找所有AJAX POST请求
                ajax_matches = AJAX_POST_PATTERN.findall(content)

                if ajax_matches:
                    # 检查是否包含CSRF令牌处理
                    has_csrf = AJAX_CSRF_PATTERN.search(content) is not None

                    if not has_csrf and file != 'auth-helper.js':  # 排除auth-helper.js，因为它已经包含全局CSRF处理
                        ajax_without_csrf.append((relative_path, len(ajax_matches)))

    if ajax_without_csrf:
        print(f"{Colors.RED}发现 {len(ajax_without_csrf)} 个可能缺少CSRF令牌处理的JavaScript文件：{Colors.RESET}")
        for path, count in ajax_without_csrf:
            print(f"  - {path} (包含 {count} 个AJAX POST请求)")
        print(f"{Colors.YELLOW}注意：如果这些文件中的AJAX请求依赖于auth-helper.js中的全局设置，则可以忽略此警告。{Colors.RESET}")
    else:
        print(f"{Colors.GREEN}所有JavaScript AJAX请求都正确处理了CSRF令牌。{Colors.RESET}")

    return ajax_without_csrf

def scan_custom_csrf_code():
    """扫描Python文件中的自定义CSRF处理代码"""
    print(f"\n{Colors.BLUE}正在扫描Python文件中的自定义CSRF处理代码...{Colors.RESET}")

    custom_csrf_files = []

    for root, dirs, files in os.walk(PYTHON_DIR):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)

                # 排除迁移脚本和__init__.py
                if 'migrations' in relative_path or file == '__init__.py':
                    continue

                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                # 查找可能的自定义CSRF处理代码，忽略注释
                matches = []
                for line in lines:
                    line = line.strip()
                    # 忽略注释行
                    if line.startswith('#'):
                        continue
                    # 查找CSRF相关代码
                    if CUSTOM_CSRF_PATTERN.search(line):
                        matches.append(line)

                if matches:
                    custom_csrf_files.append((relative_path, len(matches)))

    if custom_csrf_files:
        print(f"{Colors.YELLOW}发现 {len(custom_csrf_files)} 个可能包含自定义CSRF处理代码的Python文件：{Colors.RESET}")
        for path, count in custom_csrf_files:
            print(f"  - {path} (包含 {count} 个匹配)")
        print(f"{Colors.YELLOW}注意：这些可能是误报，请手动检查这些文件。{Colors.RESET}")
    else:
        print(f"{Colors.GREEN}未发现自定义CSRF处理代码。{Colors.RESET}")

    return custom_csrf_files

def main():
    """主函数"""
    print(f"{Colors.CYAN}===== CSRF令牌使用扫描工具 ====={Colors.RESET}")

    forms_without_csrf = scan_html_files()
    ajax_without_csrf = scan_js_files()
    custom_csrf_files = scan_custom_csrf_code()

    print(f"\n{Colors.CYAN}===== 扫描结果摘要 ====={Colors.RESET}")

    if not forms_without_csrf and not ajax_without_csrf and not custom_csrf_files:
        print(f"{Colors.GREEN}恭喜！未发现任何CSRF令牌问题。{Colors.RESET}")
        return 0
    else:
        print(f"{Colors.YELLOW}发现以下潜在问题：{Colors.RESET}")
        if forms_without_csrf:
            print(f"- {len(forms_without_csrf)} 个HTML文件可能缺少CSRF令牌")
        if ajax_without_csrf:
            print(f"- {len(ajax_without_csrf)} 个JavaScript文件可能缺少CSRF令牌处理")
        if custom_csrf_files:
            print(f"- {len(custom_csrf_files)} 个Python文件可能包含自定义CSRF处理代码")

        print(f"\n{Colors.YELLOW}请检查上述文件，确保它们正确使用了CSRF令牌。{Colors.RESET}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
