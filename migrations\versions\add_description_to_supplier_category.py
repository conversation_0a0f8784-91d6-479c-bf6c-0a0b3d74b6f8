"""
添加 description 字段到 SupplierCategory 模型
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_description_to_supplier_category'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # 添加 description 字段到 supplier_categories 表
    op.add_column('supplier_categories', sa.Column('description', sa.String(500), nullable=True))

def downgrade():
    # 删除 description 字段
    op.drop_column('supplier_categories', 'description')
