"""
修复 DATETIME2 字段的精度问题

此脚本使用系统自带的框架修复数据库中 DATETIME2 字段的精度问题。
"""
from app import db, create_app
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_datetime_precision():
    """修复 DATETIME2 字段的精度问题"""
    try:
        # 使用系统自带的会话执行SQL
        # 修复 supplier_school_relations 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'supplier_school_relations' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE supplier_school_relations 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            ALTER TABLE supplier_school_relations 
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
            
            PRINT 'supplier_school_relations 表的 created_at 和 updated_at 字段已修复';
        END
        """))
        
        # 修复 audit_logs 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'audit_logs' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE audit_logs 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            PRINT 'audit_logs 表的 created_at 字段已修复';
        END
        """))
        
        # 修复 suppliers 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'suppliers' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE suppliers 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            ALTER TABLE suppliers 
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
            
            PRINT 'suppliers 表的 created_at 和 updated_at 字段已修复';
        END
        """))
        
        # 修复 product_spec_parameters 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'product_spec_parameters' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE product_spec_parameters 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            PRINT 'product_spec_parameters 表的 created_at 字段已修复';
        END
        """))
        
        # 修复 delivery_inspections 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'delivery_inspections' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE delivery_inspections 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            ALTER TABLE delivery_inspections 
            ALTER COLUMN inspection_date DATETIME2(1) NOT NULL;
            
            PRINT 'delivery_inspections 表的 created_at 和 inspection_date 字段已修复';
        END
        """))
        
        # 修复 delivery_item_inspections 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'delivery_item_inspections' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE delivery_item_inspections 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            PRINT 'delivery_item_inspections 表的 created_at 字段已修复';
        END
        """))
        
        # 修复 supplier_products 表
        db.session.execute(text("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'supplier_products' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            ALTER TABLE supplier_products 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL;
            
            ALTER TABLE supplier_products 
            ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
            
            IF EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'supplier_products' 
                AND COLUMN_NAME = 'shelf_time'
            )
            BEGIN
                ALTER TABLE supplier_products 
                ALTER COLUMN shelf_time DATETIME2(1) NULL;
                
                PRINT 'supplier_products 表的 shelf_time 字段已修复';
            END
            
            PRINT 'supplier_products 表的 created_at 和 updated_at 字段已修复';
        END
        """))
        
        # 提交事务
        db.session.commit()
        logger.info("所有 DATETIME2 字段的精度问题已修复")
        return True
    except Exception as e:
        db.session.rollback()
        logger.error(f"修复 DATETIME2 字段精度时出错: {str(e)}")
        return False

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        if fix_datetime_precision():
            print("成功修复 DATETIME2 字段的精度问题")
        else:
            print("修复 DATETIME2 字段精度时出错，请查看日志")
