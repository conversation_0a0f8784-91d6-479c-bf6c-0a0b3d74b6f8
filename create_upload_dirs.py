import os
from app import create_app

app = create_app()

with app.app_context():
    # 确保上传目录存在
    static_folder = app.static_folder
    
    # 创建日常管理模块的上传目录
    upload_dirs = [
        os.path.join(static_folder, 'uploads'),
        os.path.join(static_folder, 'uploads', 'daily_management'),
        os.path.join(static_folder, 'uploads', 'daily_management', 'inspection'),
        os.path.join(static_folder, 'uploads', 'daily_management', 'companion'),
        os.path.join(static_folder, 'uploads', 'daily_management', 'training'),
        os.path.join(static_folder, 'uploads', 'daily_management', 'event'),
        os.path.join(static_folder, 'uploads', 'daily_management', 'issue')
    ]
    
    for directory in upload_dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")
        else:
            print(f"目录已存在: {directory}")
    
    print("上传目录检查完成")
