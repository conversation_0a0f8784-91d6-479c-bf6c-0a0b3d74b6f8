# SQL Server 数据库配置指南

本指南将帮助您配置应用程序以连接到您的SQL Server数据库。

## 配置选项

### 方式1: 使用环境变量

设置以下环境变量：

```bash
# Windows命令行
set DB_SERVER=your-server-name\instance-name
set DB_DATABASE=StudentsCMSSP
set DB_USERNAME=your-username
set DB_PASSWORD=your-password

# PowerShell
$env:DB_SERVER="your-server-name\instance-name"
$env:DB_DATABASE="StudentsCMSSP"
$env:DB_USERNAME="your-username"
$env:DB_PASSWORD="your-password"
```

### 方式2: 使用 .env 文件

1. 复制 `.env.example` 为 `.env`
2. 编辑 `.env` 文件中的数据库配置：

```ini
# SQL Server 连接配置
DB_SERVER=your-server-name\instance-name
DB_DATABASE=StudentsCMSSP
DB_USERNAME=your-username
DB_PASSWORD=your-password
DB_DRIVER=SQL Server
```

## 常见配置示例

### 本地 SQL Server Express (Windows认证)
```ini
DB_SERVER=localhost\SQLEXPRESS
DB_DATABASE=StudentsCMSSP
DB_USERNAME=
DB_PASSWORD=
```

### 本地 SQL Server Express (SQL Server认证)
```ini
DB_SERVER=localhost\SQLEXPRESS
DB_DATABASE=StudentsCMSSP
DB_USERNAME=sa
DB_PASSWORD=your-password
```

### 远程 SQL Server
```ini
DB_SERVER=*************
DB_DATABASE=StudentsCMSSP
DB_USERNAME=your-username
DB_PASSWORD=your-password
```

### 命名实例
```ini
DB_SERVER=server-name\instance-name
DB_DATABASE=StudentsCMSSP
DB_USERNAME=your-username
DB_PASSWORD=your-password
```

## 认证方式

### Windows认证 (推荐用于本地开发)
- 不设置 `DB_USERNAME` 和 `DB_PASSWORD`
- 使用当前Windows用户身份连接
- 更安全，无需在配置中存储密码

### SQL Server认证
- 设置 `DB_USERNAME` 和 `DB_PASSWORD`
- 使用SQL Server内置用户账户
- 适用于远程连接或特定用户需求

## 驱动配置

### 可用的ODBC驱动
- `SQL Server` - 旧版驱动，兼容性好
- `ODBC Driver 17 for SQL Server` - 新版驱动，性能更好
- `ODBC Driver 18 for SQL Server` - 最新驱动

### 设置驱动
```ini
DB_DRIVER=ODBC Driver 17 for SQL Server
```

## 测试数据库连接

运行测试工具检查连接：

```bash
python test_database_connection.py
```

这个工具会：
1. 显示当前配置
2. 列出可用的ODBC驱动
3. 测试pyodbc直接连接
4. 测试SQLAlchemy连接
5. 提供详细的错误信息

## 故障排除

### 常见错误及解决方案

#### 1. "Data source name not found"
- 检查 `DB_DRIVER` 设置
- 运行测试工具查看可用驱动
- 安装正确的ODBC驱动

#### 2. "Login failed for user"
- 检查用户名和密码
- 确认SQL Server允许该用户连接
- 检查数据库权限

#### 3. "Cannot open database"
- 确认数据库名称正确
- 检查用户是否有访问该数据库的权限
- 确认数据库存在

#### 4. "Server not found"
- 检查服务器名称和实例名
- 确认SQL Server服务正在运行
- 检查网络连接和防火墙设置

#### 5. "Connection timeout"
- 检查网络连接
- 增加连接超时时间
- 确认SQL Server配置允许远程连接

### 网络配置

如果连接远程SQL Server，确保：
1. SQL Server配置管理器中启用TCP/IP协议
2. 防火墙允许SQL Server端口（默认1433）
3. SQL Server Browser服务正在运行（对于命名实例）

### 权限配置

确保数据库用户具有以下权限：
- 连接到数据库服务器
- 访问指定数据库
- 执行必要的数据库操作（SELECT, INSERT, UPDATE, DELETE）

## 安全建议

1. **生产环境**：
   - 使用专用的数据库用户账户
   - 设置强密码
   - 限制用户权限到最小必需
   - 使用加密连接

2. **开发环境**：
   - 可以使用Windows认证简化配置
   - 定期更新密码
   - 不要在代码中硬编码密码

3. **配置文件安全**：
   - 将 `.env` 文件添加到 `.gitignore`
   - 不要提交包含密码的配置文件
   - 使用环境变量管理敏感信息

## 高级配置

### 连接池设置
在 `config.py` 中已配置了连接池参数：
- `pool_size`: 连接池大小 (10)
- `max_overflow`: 最大溢出连接 (5)
- `pool_timeout`: 连接超时 (20秒)
- `pool_recycle`: 连接回收时间 (280秒)

### 性能优化
- 使用最新的ODBC驱动
- 适当调整连接池大小
- 启用连接池预检查
- 监控数据库连接使用情况
