# Flask Application Configuration
# Copy this file to .env and modify as needed

# Server Configuration
HOST=0.0.0.0
PORT=5000

# SSL/HTTPS Configuration
# Set to 'true' to enable HTTPS, 'false' for HTTP only
SSL_ENABLED=false

# SSL Context - can be 'adhoc' for auto-generated certificate
# or specify custom certificate files as 'cert.pem,key.pem'
SSL_CONTEXT=adhoc

# Database Configuration
# 完整的数据库URL（如果设置，将覆盖下面的单独配置）
DATABASE_URL=

# SQL Server 连接配置
DB_SERVER=localhost\SQLEXPRESS
DB_DATABASE=StudentsCMSSP
# 留空使用Windows认证，或设置用户名密码使用SQL Server认证
DB_USERNAME=
DB_PASSWORD=
DB_DRIVER=SQL Server

# Security Configuration
SECRET_KEY=hard-to-guess-string

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# API Configuration
API_KEY=system_fix_api_key_2025

# Logging Configuration
LOG_LEVEL=INFO
