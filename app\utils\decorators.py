"""
装饰器模块
提供权限检查和错误处理装饰器
"""
from functools import wraps
from flask import flash, redirect, url_for, request, jsonify, current_app
from flask_login import current_user

# 导入周菜单服务模块的异常类
# 注意：这里使用延迟导入，避免循环导入问题
WeeklyMenuError = None

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))

        if not current_user.is_admin():
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('main.index'))

        return f(*args, **kwargs)

    return decorated_function

def check_permission(module, action):
    """
    检查用户是否有指定模块和操作的权限
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login', next=request.url))

            # 系统管理员拥有所有权限
            if current_user.is_admin():
                return f(*args, **kwargs)

            # 检查用户是否有权限
            if not current_user.has_permission(module, action):
                flash('您没有权限执行此操作', 'danger')
                return redirect(url_for('main.index'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def area_required(level=None, check_area_param=0, area_id_param='id'):
    """
    检查用户是否有权限访问特定级别的区域
    level: 1:县市区, 2:乡镇, 3:学校, 4:食堂，None表示任意级别
    check_area_param: 是否检查URL参数中的区域ID
    area_id_param: URL参数中区域ID的参数名
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login', next=request.url))

            # 系统管理员可以访问所有级别
            if current_user.is_admin():
                return f(*args, **kwargs)

            # 检查用户区域级别
            if level and current_user.area_level > level:
                flash('您没有权限访问此功能', 'danger')
                return redirect(url_for('main.index'))

            # 检查URL参数中的区域ID
            if check_area_param and area_id_param in kwargs:
                area_id = kwargs[area_id_param]
                if not current_user.can_access_area_by_id(area_id):
                    flash('您没有权限访问此区域', 'danger')
                    return redirect(url_for('main.index'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def handle_weekly_menu_error(func):
    """
    处理周菜单操作异常的装饰器

    参数:
        func: 被装饰的函数

    返回:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 延迟导入WeeklyMenuError，避免循环导入
        global WeeklyMenuError
        if WeeklyMenuError is None:
            from app.services.weekly_menu_service import WeeklyMenuError as WME
            WeeklyMenuError = WME

        try:
            return func(*args, **kwargs)
        except WeeklyMenuError as e:
            current_app.logger.error(f"周菜单操作错误: {str(e)}")
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': False,
                    'message': str(e)
                })
            flash(str(e), 'danger')
            return redirect(url_for('weekly_menu.index'))
        except Exception as e:
            current_app.logger.exception(f"周菜单操作异常: {str(e)}")
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': False,
                    'message': '服务器内部错误'
                })
            flash('操作失败，请稍后重试', 'danger')
            return redirect(url_for('weekly_menu.index'))
    return wrapper