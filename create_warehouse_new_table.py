"""
创建新的仓库表

此脚本用于创建新的仓库表，确保datetime字段的精度正确。
"""
import os
import sys
import logging
import pyodbc
from datetime import datetime

# 配置日志
log_file = f"create_warehouse_new_table_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接信息
DB_SERVER = os.environ.get('DB_SERVER', 'localhost')
DB_NAME = os.environ.get('DB_NAME', 'StudentsCMSSP')
DB_USER = os.environ.get('DB_USER', 'sa')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Passw0rd')
DB_DRIVER = os.environ.get('DB_DRIVER', 'ODBC Driver 17 for SQL Server')

def get_connection():
    """获取数据库连接"""
    conn_str = f'DRIVER={{{DB_DRIVER}}};SERVER={DB_SERVER};DATABASE={DB_NAME};UID={DB_USER};PWD={DB_PASSWORD}'
    try:
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        logger.error(f"连接数据库时出错: {e}")
        return None

def create_warehouse_new_table():
    """创建新的仓库表"""
    conn = get_connection()
    if not conn:
        logger.error("无法连接数据库，退出")
        return False
    
    try:
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("""
        IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[warehouses_new]') AND type in (N'U'))
        BEGIN
            DROP TABLE [dbo].[warehouses_new]
        END
        """)
        
        # 创建新表
        cursor.execute("""
        CREATE TABLE [dbo].[warehouses_new](
            [id] [int] IDENTITY(1,1) NOT NULL,
            [name] [nvarchar](100) NOT NULL,
            [area_id] [int] NOT NULL,
            [location] [nvarchar](200) NOT NULL,
            [manager_id] [int] NOT NULL,
            [capacity] [float] NULL,
            [capacity_unit] [nvarchar](20) NULL,
            [temperature_range] [nvarchar](50) NULL,
            [humidity_range] [nvarchar](50) NULL,
            [status] [nvarchar](20) NOT NULL DEFAULT ('正常'),
            [notes] [nvarchar](max) NULL,
            [created_at] [datetime] NOT NULL DEFAULT (getdate()),
            [updated_at] [datetime] NOT NULL DEFAULT (getdate()),
            CONSTRAINT [PK_warehouses_new] PRIMARY KEY CLUSTERED 
            (
                [id] ASC
            )
        )
        """)
        
        # 添加外键约束
        cursor.execute("""
        ALTER TABLE [dbo].[warehouses_new] WITH CHECK ADD CONSTRAINT [FK_warehouses_new_administrative_areas] FOREIGN KEY([area_id])
        REFERENCES [dbo].[administrative_areas] ([id])
        """)
        
        cursor.execute("""
        ALTER TABLE [dbo].[warehouses_new] WITH CHECK ADD CONSTRAINT [FK_warehouses_new_users] FOREIGN KEY([manager_id])
        REFERENCES [dbo].[users] ([id])
        """)
        
        conn.commit()
        logger.info("成功创建warehouses_new表")
        return True
    except Exception as e:
        logger.error(f"创建warehouses_new表时出错: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "创建新的仓库表")
    print("="*80)
    print(f"\n日志文件: {log_file}")
    
    # 创建新的仓库表
    print("\n创建新的仓库表...")
    if create_warehouse_new_table():
        print("  ✓ 成功创建warehouses_new表")
    else:
        print("  ✗ 创建warehouses_new表失败，请查看日志")
    
    print("\n创建完成！")
    print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
