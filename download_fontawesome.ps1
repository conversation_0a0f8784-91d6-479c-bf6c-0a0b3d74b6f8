$fontFiles = @(
    "fa-brands-400.eot",
    "fa-brands-400.svg",
    "fa-brands-400.ttf",
    "fa-brands-400.woff",
    "fa-brands-400.woff2",
    "fa-regular-400.eot",
    "fa-regular-400.svg",
    "fa-regular-400.ttf",
    "fa-regular-400.woff",
    "fa-regular-400.woff2",
    "fa-solid-900.eot",
    "fa-solid-900.svg",
    "fa-solid-900.ttf",
    "fa-solid-900.woff",
    "fa-solid-900.woff2"
)

foreach ($file in $fontFiles) {
    $url = "https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.3/webfonts/$file"
    $output = "app/static/vendor/fontawesome/webfonts/$file"
    Write-Host "Downloading $file..."
    Invoke-WebRequest -Uri $url -OutFile $output
}

Write-Host "All Font Awesome webfonts downloaded successfully!"
