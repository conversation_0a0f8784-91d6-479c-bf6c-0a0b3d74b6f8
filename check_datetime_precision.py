"""
检查数据库中datetime2字段的精度
"""

import pyodbc
import sys

# 配置信息
SQL_SERVER = 'localhost\\SQLEXPRESS'
SQL_SERVER_DB = 'StudentsCMSSP'

def get_sqlserver_connection():
    """获取SQL Server数据库连接"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={SQL_SERVER};DATABASE={SQL_SERVER_DB};Trusted_Connection=yes"
        conn = pyodbc.connect(conn_str)
        print(f"已成功连接到SQL Server数据库: {SQL_SERVER_DB}")
        return conn
    except pyodbc.Error as e:
        print(f"连接SQL Server数据库时出错: {e}")
        return None

def check_datetime_precision(conn, table_name=None):
    """检查数据库中datetime2字段的精度"""
    cursor = conn.cursor()
    
    try:
        query = """
        SELECT 
            t.name AS TableName,
            c.name AS ColumnName,
            ty.name AS DataType,
            c.max_length AS MaxLength,
            c.precision AS Precision,
            c.scale AS Scale
        FROM 
            sys.columns c
        JOIN 
            sys.tables t ON c.object_id = t.object_id
        JOIN 
            sys.types ty ON c.system_type_id = ty.system_type_id
        WHERE 
            ty.name = 'datetime2'
        """
        
        if table_name:
            query += f" AND t.name = '{table_name}'"
            
        query += " ORDER BY t.name, c.name"
        
        cursor.execute(query)
        
        results = cursor.fetchall()
        
        if not results:
            print("未找到任何datetime2类型的字段")
            return
            
        print(f"{'表名':<30} {'列名':<30} {'数据类型':<15} {'最大长度':<10} {'精度':<10} {'小数位数':<10}")
        print("-" * 100)
        
        for row in results:
            print(f"{row.TableName:<30} {row.ColumnName:<30} {row.DataType:<15} {row.MaxLength:<10} {row.Precision:<10} {row.Scale:<10}")
            
    except pyodbc.Error as e:
        print(f"查询数据库时出错: {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    conn = get_sqlserver_connection()
    if not conn:
        print("无法连接到数据库，退出程序")
        sys.exit(1)
        
    try:
        # 检查area_change_history表的datetime2字段精度
        print("\n检查area_change_history表的datetime2字段精度:")
        check_datetime_precision(conn, 'area_change_history')
        
        # 检查所有表的datetime2字段精度
        print("\n检查所有表的datetime2字段精度:")
        check_datetime_precision(conn)
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
