"""
食谱高级功能数据库迁移脚本
此脚本用于创建食谱高级功能所需的数据库表和字段
包括：食谱评价和反馈、食谱模板和变种、基于库存的推荐、搜索和筛选优化
"""

from app import db
from app.models import Recipe, Ingredient
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import text, text
from datetime import datetime
import os
import sys

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def upgrade():
    """执行数据库升级"""
    # 1. 修改recipes表，添加模板和变种相关字段
    add_columns_to_recipes()

    # 2. 创建食谱评价和反馈相关表
    create_recipe_review_tables()

    # 3. 创建食谱版本管理表
    create_recipe_version_tables()

    # 4. 创建食材替代和季节性信息表
    create_recipe_ingredient_alternative_tables()

    # 5. 创建标签和搜索相关表
    create_recipe_tag_tables()

    print("数据库迁移完成！")

def add_columns_to_recipes():
    """向recipes表添加新字段"""
    # 检查字段是否已存在，不存在则添加
    columns_to_add = [
        ('parent_id', 'INTEGER'),
        ('is_template', 'BOOLEAN'),
        ('template_type', 'VARCHAR(50)'),
        ('variation_reason', 'TEXT'),
        ('version', 'INTEGER')
    ]

    with db.engine.connect() as conn:
        # 获取现有列信息
        result = conn.execute('PRAGMA table_info(recipes)')
        existing_columns = [col['name'] for col in result.fetchall()]

        for col_name, col_type in columns_to_add:
            if col_name not in existing_columns:
                if col_name == 'is_template':
                    conn.execute(f'ALTER TABLE recipes ADD COLUMN {col_name} {col_type} DEFAULT FALSE')
                elif col_name == 'version':
                    conn.execute(f'ALTER TABLE recipes ADD COLUMN {col_name} {col_type} DEFAULT 1')
                else:
                    conn.execute(f'ALTER TABLE recipes ADD COLUMN {col_name} {col_type}')
                print(f"已添加字段: recipes.{col_name}")

        # 添加外键约束
        if 'parent_id' in [col[0] for col in columns_to_add]:
            try:
                conn.execute('CREATE INDEX IF NOT EXISTS idx_recipes_parent_id ON recipes(parent_id)')
                print("已创建索引: idx_recipes_parent_id")
            except Exception as e:
                print(f"创建索引时出错: {e}")

def create_recipe_review_tables():
    """创建食谱评价和反馈相关表"""
    with db.engine.connect() as conn:
        # 创建食谱评价表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_reviews (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipe_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            area_id INTEGER NOT NULL,
            rating INTEGER NOT NULL CHECK(rating BETWEEN 1 AND 5),
            comment TEXT,
            usage_date DATE,
            is_public BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
        )
        ''')
        print("已创建表: recipe_reviews")

        # 创建食谱评价图片表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_review_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            review_id INTEGER NOT NULL,
            image_path TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (review_id) REFERENCES recipe_reviews(id) ON DELETE CASCADE
        )
        ''')
        print("已创建表: recipe_review_images")

        # 创建食谱评价标签表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_review_tags (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            review_id INTEGER NOT NULL,
            tag_name VARCHAR(50) NOT NULL,
            FOREIGN KEY (review_id) REFERENCES recipe_reviews(id) ON DELETE CASCADE
        )
        ''')
        print("已创建表: recipe_review_tags")

        # 创建食谱改进建议表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_improvement_suggestions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipe_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            suggestion TEXT NOT NULL,
            is_adopted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        ''')
        print("已创建表: recipe_improvement_suggestions")

        # 创建索引
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_reviews_recipe_id ON recipe_reviews(recipe_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_reviews_user_id ON recipe_reviews(user_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_reviews_area_id ON recipe_reviews(area_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_review_images_review_id ON recipe_review_images(review_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_review_tags_review_id ON recipe_review_tags(review_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_improvement_suggestions_recipe_id ON recipe_improvement_suggestions(recipe_id)')

def create_recipe_version_tables():
    """创建食谱版本管理表"""
    with db.engine.connect() as conn:
        # 创建食谱版本表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_versions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipe_id INTEGER NOT NULL,
            version INTEGER NOT NULL,
            data TEXT NOT NULL,
            changed_by INTEGER NOT NULL,
            change_reason TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id),
            FOREIGN KEY (changed_by) REFERENCES users(id)
        )
        ''')
        print("已创建表: recipe_versions")

        # 创建索引
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_versions_recipe_id ON recipe_versions(recipe_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_versions_changed_by ON recipe_versions(changed_by)')

def create_recipe_ingredient_alternative_tables():
    """创建食材替代和季节性信息表"""
    with db.engine.connect() as conn:
        # 创建食材替代表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_ingredient_alternatives (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipe_id INTEGER NOT NULL,
            original_ingredient_id INTEGER NOT NULL,
            alternative_ingredient_id INTEGER NOT NULL,
            conversion_ratio FLOAT DEFAULT 1.0,
            notes TEXT,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id),
            FOREIGN KEY (original_ingredient_id) REFERENCES ingredients(id),
            FOREIGN KEY (alternative_ingredient_id) REFERENCES ingredients(id)
        )
        ''')
        print("已创建表: recipe_ingredient_alternatives")

        # 创建季节性信息表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_seasonal_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipe_id INTEGER NOT NULL,
            season VARCHAR(20) NOT NULL,
            suitability_score INTEGER DEFAULT 3 CHECK(suitability_score BETWEEN 1 AND 5),
            notes TEXT,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id)
        )
        ''')
        print("已创建表: recipe_seasonal_info")

        # 创建索引
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_ingredient_alternatives_recipe_id ON recipe_ingredient_alternatives(recipe_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_ingredient_alternatives_original_id ON recipe_ingredient_alternatives(original_ingredient_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_ingredient_alternatives_alternative_id ON recipe_ingredient_alternatives(alternative_ingredient_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_seasonal_info_recipe_id ON recipe_seasonal_info(recipe_id)')

def create_recipe_tag_tables():
    """创建标签和搜索相关表"""
    with db.engine.connect() as conn:
        # 创建食谱标签表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_tags (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL UNIQUE,
            category VARCHAR(50),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        print("已创建表: recipe_tags")

        # 创建食谱标签关联表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS recipe_tag_relations (
            recipe_id INTEGER NOT NULL,
            tag_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (recipe_id, tag_id),
            FOREIGN KEY (recipe_id) REFERENCES recipes(id),
            FOREIGN KEY (tag_id) REFERENCES recipe_tags(id)
        )
        ''')
        print("已创建表: recipe_tag_relations")

        # 创建用户食谱收藏表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS user_recipe_favorites (
            user_id INTEGER NOT NULL,
            recipe_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, recipe_id),
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (recipe_id) REFERENCES recipes(id)
        )
        ''')
        print("已创建表: user_recipe_favorites")

        # 创建用户搜索历史表
        conn.execute('''
        CREATE TABLE IF NOT EXISTS user_search_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            search_query TEXT NOT NULL,
            search_filters TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        ''')
        print("已创建表: user_search_history")

        # 创建索引
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_tags_name ON recipe_tags(name)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_tags_category ON recipe_tags(category)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_tag_relations_recipe_id ON recipe_tag_relations(recipe_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_recipe_tag_relations_tag_id ON recipe_tag_relations(tag_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_user_recipe_favorites_user_id ON user_recipe_favorites(user_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_user_recipe_favorites_recipe_id ON user_recipe_favorites(recipe_id)')
        conn.execute('CREATE INDEX IF NOT EXISTS idx_user_search_history_user_id ON user_search_history(user_id)')

def downgrade():
    """执行数据库降级（删除所有创建的表和字段）"""
    with db.engine.connect() as conn:
        # 删除创建的表
        tables_to_drop = [
            'user_search_history',
            'user_recipe_favorites',
            'recipe_tag_relations',
            'recipe_tags',
            'recipe_seasonal_info',
            'recipe_ingredient_alternatives',
            'recipe_versions',
            'recipe_improvement_suggestions',
            'recipe_review_tags',
            'recipe_review_images',
            'recipe_reviews'
        ]

        for table in tables_to_drop:
            try:
                conn.execute(f'DROP TABLE IF EXISTS {table}')
                print(f"已删除表: {table}")
            except Exception as e:
                print(f"删除表 {table} 时出错: {e}")

        # 从recipes表中删除添加的字段
        # SQLite不支持直接删除列，需要创建新表并复制数据
        # 此处省略具体实现，实际降级时需要谨慎处理

        print("数据库降级完成！")

if __name__ == '__main__':
    # 如果直接运行此脚本，则执行升级
    upgrade()
