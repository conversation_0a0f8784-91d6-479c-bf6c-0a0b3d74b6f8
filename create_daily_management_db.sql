-- 创建食堂日常管理相关表

-- 首先删除可能引用 daily_logs 的表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'inspection_records')
BEGIN
    DROP TABLE inspection_records;
    PRINT '已删除 inspection_records 表';
END

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'dining_companions')
BEGIN
    DROP TABLE dining_companions;
    PRINT '已删除 dining_companions 表';
END

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'canteen_training_records')
BEGIN
    DROP TABLE canteen_training_records;
    PRINT '已删除 canteen_training_records 表';
END

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'special_events')
BEGIN
    DROP TABLE special_events;
    PRINT '已删除 special_events 表';
END

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'issues')
BEGIN
    DROP TABLE issues;
    PRINT '已删除 issues 表';
END

-- 如果存在 photos 表，删除它
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'photos')
BEGIN
    DROP TABLE photos;
    PRINT '已删除 photos 表';
END

-- 然后删除 daily_logs 表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'daily_logs')
BEGIN
    DROP TABLE daily_logs;
    PRINT '已删除 daily_logs 表';
END

CREATE TABLE daily_logs (
    id INT PRIMARY KEY IDENTITY(1,1),
    log_date DATE NOT NULL UNIQUE,
    weather NVARCHAR(50),
    manager NVARCHAR(100),
    student_count INT DEFAULT 0,
    teacher_count INT DEFAULT 0,
    other_count INT DEFAULT 0,
    breakfast_menu NVARCHAR(MAX),
    lunch_menu NVARCHAR(MAX),
    dinner_menu NVARCHAR(MAX),
    food_waste FLOAT,
    special_events NVARCHAR(MAX),
    operation_summary NVARCHAR(MAX),
    area_id INT,
    created_by INT,
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    CONSTRAINT FK_daily_log_area FOREIGN KEY (area_id) REFERENCES administrative_areas(id),
    CONSTRAINT FK_daily_log_user FOREIGN KEY (created_by) REFERENCES users(id)
);

CREATE INDEX idx_daily_logs_date ON daily_logs(log_date);
PRINT '已创建 daily_logs 表';

-- 检查记录表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'inspection_records')
BEGIN
    DROP TABLE inspection_records;
    PRINT '已删除 inspection_records 表';
END

CREATE TABLE inspection_records (
    id INT PRIMARY KEY IDENTITY(1,1),
    daily_log_id INT NOT NULL,
    inspection_type VARCHAR(10) NOT NULL CHECK (inspection_type IN ('morning', 'noon', 'evening')),
    inspection_item NVARCHAR(100) NOT NULL,
    status VARCHAR(10) DEFAULT 'normal' CHECK (status IN ('normal', 'abnormal')),
    description NVARCHAR(MAX),
    photo_path NVARCHAR(255),  -- 照片路径
    inspector_id INT,
    inspection_time DATETIME2(1) DEFAULT GETDATE(),
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    CONSTRAINT FK_inspection_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id) ON DELETE CASCADE,
    CONSTRAINT FK_inspection_inspector FOREIGN KEY (inspector_id) REFERENCES users(id)
);

CREATE INDEX idx_inspection_daily_log ON inspection_records(daily_log_id);
PRINT '已创建 inspection_records 表';

-- 陪餐记录表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'dining_companions')
BEGIN
    DROP TABLE dining_companions;
    PRINT '已删除 dining_companions 表';
END

CREATE TABLE dining_companions (
    id INT PRIMARY KEY IDENTITY(1,1),
    daily_log_id INT NOT NULL,
    companion_name NVARCHAR(100) NOT NULL,
    companion_role NVARCHAR(100) NOT NULL,
    meal_type VARCHAR(10) NOT NULL CHECK (meal_type IN ('breakfast', 'lunch', 'dinner')),
    dining_time DATETIME2(1) NOT NULL,
    taste_rating INT,
    hygiene_rating INT,
    service_rating INT,
    comments NVARCHAR(MAX),
    suggestions NVARCHAR(MAX),
    photo_paths NVARCHAR(MAX),  -- 存储多个照片路径，用分号分隔
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    CONSTRAINT FK_companion_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id) ON DELETE CASCADE
);

CREATE INDEX idx_companion_daily_log ON dining_companions(daily_log_id);
PRINT '已创建 dining_companions 表';

-- 培训记录表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'canteen_training_records')
BEGIN
    DROP TABLE canteen_training_records;
    PRINT '已删除 canteen_training_records 表';
END

CREATE TABLE canteen_training_records (
    id INT PRIMARY KEY IDENTITY(1,1),
    daily_log_id INT NOT NULL,
    training_topic NVARCHAR(200) NOT NULL,
    trainer NVARCHAR(100) NOT NULL,
    training_time DATETIME2(1) NOT NULL,
    location NVARCHAR(100),
    duration INT,  -- 培训时长(分钟)
    attendees_count INT,  -- 参训人数
    content_summary NVARCHAR(MAX),
    effectiveness_evaluation NVARCHAR(MAX),
    photo_paths NVARCHAR(MAX),  -- 存储多个照片路径，用分号分隔
    created_by INT,
    area_id INT,
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    CONSTRAINT FK_training_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id) ON DELETE CASCADE,
    CONSTRAINT FK_training_user FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT FK_training_area FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
);

CREATE INDEX idx_training_daily_log ON canteen_training_records(daily_log_id);
PRINT '已创建 canteen_training_records 表';

-- 特殊事件表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'special_events')
BEGIN
    DROP TABLE special_events;
    PRINT '已删除 special_events 表';
END

CREATE TABLE special_events (
    id INT PRIMARY KEY IDENTITY(1,1),
    daily_log_id INT NOT NULL,
    event_type NVARCHAR(100) NOT NULL,
    event_time DATETIME2(1) NOT NULL,
    description NVARCHAR(MAX) NOT NULL,
    participants NVARCHAR(MAX),
    handling_measures NVARCHAR(MAX),
    event_summary NVARCHAR(MAX),
    photo_paths NVARCHAR(MAX),  -- 存储多个照片路径，用分号分隔
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    CONSTRAINT FK_event_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id) ON DELETE CASCADE
);

CREATE INDEX idx_event_daily_log ON special_events(daily_log_id);
PRINT '已创建 special_events 表';

-- 问题记录表
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'issues')
BEGIN
    DROP TABLE issues;
    PRINT '已删除 issues 表';
END

CREATE TABLE issues (
    id INT PRIMARY KEY IDENTITY(1,1),
    daily_log_id INT NOT NULL,
    issue_type NVARCHAR(100) NOT NULL,
    description NVARCHAR(MAX) NOT NULL,
    status VARCHAR(10) DEFAULT 'pending' CHECK (status IN ('pending', 'fixing', 'fixed')),
    found_time DATETIME2(1) NOT NULL,
    fixed_time DATETIME2(1),
    responsible_person NVARCHAR(100),
    verification_result NVARCHAR(MAX),
    photo_paths NVARCHAR(MAX),  -- 存储多个照片路径，用分号分隔
    created_at DATETIME2(1) DEFAULT GETDATE(),
    updated_at DATETIME2(1) DEFAULT GETDATE(),
    CONSTRAINT FK_issue_daily_log FOREIGN KEY (daily_log_id) REFERENCES daily_logs(id) ON DELETE CASCADE
);

CREATE INDEX idx_issue_daily_log ON issues(daily_log_id);
CREATE INDEX idx_issue_status ON issues(status);
PRINT '已创建 issues 表';

-- 如果存在 photos 表，删除它
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'photos')
BEGIN
    DROP TABLE photos;
    PRINT '已删除 photos 表';
END

PRINT '所有表创建完成';
