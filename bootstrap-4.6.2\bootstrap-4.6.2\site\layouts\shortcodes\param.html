{{- /*
  Work around wrong escapes in integrity attributes.
  Original: https://github.com/gohugoio/hugo/blob/master/tpl/tplimpl/embedded/templates/shortcodes/param.html
*/ -}}

{{- $name := .Get 0 -}}
{{- with $name -}}
{{- $value := $.Page.Param . -}}
{{- /* If any parameter ends with `_hash`, mark the string as safe HTML */ -}}
{{- if (strings.HasSuffix $name "_hash") -}}
  {{- $value = $value | safeHTML -}}
{{- end -}}
{{- with $value }}{{ . }}{{ else }}{{ errorf "Param %q not found: %s" $name $.Position }}{{ end -}}
{{- else }}{{ errorf "Missing param key: %s" $.Position }}{{ end -}}
