-- 修复 supplier_school_relations 表的 DATETIME2 字段精度问题

-- 检查表是否存在
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'supplier_school_relations'
)
BEGIN
    -- 修复 created_at 字段
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'supplier_school_relations' 
        AND COLUMN_NAME = 'created_at'
    )
    BEGIN
        ALTER TABLE supplier_school_relations 
        ALTER COLUMN created_at DATETIME2(1) NOT NULL;
        
        PRINT 'supplier_school_relations 表的 created_at 字段已修复';
    END
    
    -- 修复 updated_at 字段
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'supplier_school_relations' 
        AND COLUMN_NAME = 'updated_at'
    )
    BEGIN
        ALTER TABLE supplier_school_relations 
        ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
        
        PRINT 'supplier_school_relations 表的 updated_at 字段已修复';
    END
END
ELSE
BEGIN
    PRINT 'supplier_school_relations 表不存在';
END

-- 修复 audit_logs 表的 created_at 字段
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'audit_logs'
)
BEGIN
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'audit_logs' 
        AND COLUMN_NAME = 'created_at'
    )
    BEGIN
        ALTER TABLE audit_logs 
        ALTER COLUMN created_at DATETIME2(1) NOT NULL;
        
        PRINT 'audit_logs 表的 created_at 字段已修复';
    END
END
ELSE
BEGIN
    PRINT 'audit_logs 表不存在';
END

-- 修复 suppliers 表的 created_at 和 updated_at 字段
IF EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_NAME = 'suppliers'
)
BEGIN
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'suppliers' 
        AND COLUMN_NAME = 'created_at'
    )
    BEGIN
        ALTER TABLE suppliers 
        ALTER COLUMN created_at DATETIME2(1) NOT NULL;
        
        PRINT 'suppliers 表的 created_at 字段已修复';
    END
    
    IF EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'suppliers' 
        AND COLUMN_NAME = 'updated_at'
    )
    BEGIN
        ALTER TABLE suppliers 
        ALTER COLUMN updated_at DATETIME2(1) NOT NULL;
        
        PRINT 'suppliers 表的 updated_at 字段已修复';
    END
END
ELSE
BEGIN
    PRINT 'suppliers 表不存在';
END

PRINT '所有 DATETIME2 字段的精度问题已修复';
