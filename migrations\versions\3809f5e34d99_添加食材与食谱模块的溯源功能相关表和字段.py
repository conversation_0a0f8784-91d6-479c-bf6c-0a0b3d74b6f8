"""添加食材与食谱模块的溯源功能相关表和字段

Revision ID: 3809f5e34d99
Revises: 45a209c75855
Create Date: 2025-05-05 18:01:17.545246

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3809f5e34d99'
down_revision = '45a209c75855'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ingredient_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('description', sa.String(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['ingredient_categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('recipe_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('recipe_processes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('recipe_id', sa.Integer(), nullable=False),
    sa.Column('process_name', sa.String(length=100), nullable=False),
    sa.Column('process_order', sa.Integer(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('estimated_time', sa.Integer(), nullable=True),
    sa.Column('image', sa.String(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['recipe_id'], ['recipes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('recipe_process_ingredients',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('process_id', sa.Integer(), nullable=False),
    sa.Column('ingredient_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('processing_method', sa.String(length=100), nullable=True),
    sa.Column('notes', sa.String(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['ingredient_id'], ['ingredients.id'], ),
    sa.ForeignKeyConstraint(['process_id'], ['recipe_processes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('ingredients', schema=None) as batch_op:
        batch_op.add_column(sa.Column('category_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('storage_condition', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('specification', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('nutrition_info', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('status', sa.Integer(), server_default='1', nullable=False))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
        batch_op.create_foreign_key('fk_ingredient_category', 'ingredient_categories', ['category_id'], ['id'])

    with op.batch_alter_table('recipes', schema=None) as batch_op:
        batch_op.add_column(sa.Column('category_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('meal_type', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('description', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('nutrition_info', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('cooking_method', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('cooking_time', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('serving_size', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('status', sa.Integer(), server_default='1', nullable=False))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
        batch_op.create_foreign_key('fk_recipe_category', 'recipe_categories', ['category_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('recipes', schema=None) as batch_op:
        batch_op.drop_constraint('fk_recipe_category', type_='foreignkey')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('status')
        batch_op.drop_column('serving_size')
        batch_op.drop_column('cooking_time')
        batch_op.drop_column('cooking_method')
        batch_op.drop_column('nutrition_info')
        batch_op.drop_column('description')
        batch_op.drop_column('meal_type')
        batch_op.drop_column('category_id')

    with op.batch_alter_table('ingredients', schema=None) as batch_op:
        batch_op.drop_constraint('fk_ingredient_category', type_='foreignkey')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('status')
        batch_op.drop_column('nutrition_info')
        batch_op.drop_column('specification')
        batch_op.drop_column('storage_condition')
        batch_op.drop_column('category_id')

    op.drop_table('recipe_process_ingredients')
    op.drop_table('recipe_processes')
    op.drop_table('recipe_categories')
    op.drop_table('ingredient_categories')
    # ### end Alembic commands ###
