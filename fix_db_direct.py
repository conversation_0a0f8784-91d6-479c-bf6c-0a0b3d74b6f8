"""
直接修复数据库表结构

此脚本直接连接到SQL Server数据库，修复所有表中的datetime字段。
"""
import pyodbc
import sys
import logging
from datetime import datetime

# 配置信息
SQL_SERVER = 'localhost\\SQLEXPRESS'
SQL_SERVER_DB = 'StudentsCMSSP'

# 配置日志
log_file = f"fix_db_direct_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_sqlserver_connection():
    """获取SQL Server数据库连接"""
    try:
        conn_str = f'DRIVER={{SQL Server}};SERVER={SQL_SERVER};DATABASE={SQL_SERVER_DB};Trusted_Connection=yes;'
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        logger.error(f"错误: 无法连接到SQL Server: {e}")
        return None

def fix_warehouses_table(conn):
    """修复warehouses表"""
    cursor = conn.cursor()
    try:
        # 检查表是否存在
        cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'warehouses'")
        if cursor.fetchone()[0] == 0:
            logger.warning("warehouses表不存在，跳过")
            return False
        
        # 修复created_at字段
        cursor.execute("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'warehouses' 
            AND COLUMN_NAME = 'created_at'
        )
        BEGIN
            -- 查找默认约束
            DECLARE @ConstraintName nvarchar(200)
            
            SELECT @ConstraintName = dc.name
            FROM sys.default_constraints dc
            JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
            WHERE OBJECT_NAME(dc.parent_object_id) = 'warehouses' AND c.name = 'created_at'
            
            -- 删除默认约束
            IF @ConstraintName IS NOT NULL
            BEGIN
                EXEC('ALTER TABLE warehouses DROP CONSTRAINT ' + @ConstraintName)
                PRINT 'warehouses表的created_at默认约束已删除'
            END
            
            -- 修改列类型
            ALTER TABLE warehouses 
            ALTER COLUMN created_at DATETIME2(1) NOT NULL
            
            -- 添加新的默认约束
            ALTER TABLE warehouses
            ADD CONSTRAINT DF_warehouses_created_at DEFAULT (GETDATE()) FOR created_at
            
            PRINT 'warehouses表的created_at字段已修复'
        END
        """)
        
        # 修复updated_at字段
        cursor.execute("""
        IF EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'warehouses' 
            AND COLUMN_NAME = 'updated_at'
        )
        BEGIN
            -- 查找默认约束
            DECLARE @ConstraintName nvarchar(200)
            
            SELECT @ConstraintName = dc.name
            FROM sys.default_constraints dc
            JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
            WHERE OBJECT_NAME(dc.parent_object_id) = 'warehouses' AND c.name = 'updated_at'
            
            -- 删除默认约束
            IF @ConstraintName IS NOT NULL
            BEGIN
                EXEC('ALTER TABLE warehouses DROP CONSTRAINT ' + @ConstraintName)
                PRINT 'warehouses表的updated_at默认约束已删除'
            END
            
            -- 修改列类型
            ALTER TABLE warehouses 
            ALTER COLUMN updated_at DATETIME2(1) NULL
            
            -- 添加新的默认约束
            ALTER TABLE warehouses
            ADD CONSTRAINT DF_warehouses_updated_at DEFAULT (GETDATE()) FOR updated_at
            
            PRINT 'warehouses表的updated_at字段已修复'
        END
        """)
        
        conn.commit()
        logger.info("warehouses表修复完成")
        return True
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"修复warehouses表时出错: {e}")
        return False
    finally:
        cursor.close()

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "直接修复数据库表结构")
    print("="*80)
    print(f"\n日志文件: {log_file}")
    
    # 获取数据库连接
    conn = get_sqlserver_connection()
    if not conn:
        print("无法连接到数据库，程序退出")
        sys.exit(1)
    
    try:
        # 修复warehouses表
        print("\n修复warehouses表...")
        if fix_warehouses_table(conn):
            print("  ✓ warehouses表修复成功")
        else:
            print("  ✗ warehouses表修复失败，请查看日志")
        
        print("\n修复完成！")
    except Exception as e:
        logger.error(f"程序执行时出错: {e}")
        print(f"\n错误: {e}")
    finally:
        conn.close()
        print("\n数据库连接已关闭")
        print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
