"""
检查并修复数据库中datetime字段的精度问题
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app, db
from sqlalchemy import text, inspect

def check_and_fix_datetime_precision():
    """检查并修复数据库中datetime字段的精度问题"""
    app = create_app()

    with app.app_context():
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()

        print("开始检查数据库中的datetime字段精度...")

        # 存储需要修复的表和字段
        tables_to_fix = {}

        # 检查所有表
        for table_name in tables:
            columns = inspector.get_columns(table_name)
            datetime_columns = []

            for column in columns:
                # 检查列类型是否为datetime且精度不为1
                if 'DATETIME' in str(column['type']).upper() and '(1)' not in str(column['type']):
                    datetime_columns.append(column['name'])

            if datetime_columns:
                tables_to_fix[table_name] = datetime_columns

        # 显示检查结果
        if not tables_to_fix:
            print("所有datetime字段精度均正确，无需修复。")
            return

        print("\n发现以下表的datetime字段需要修复精度：")
        for table, columns in tables_to_fix.items():
            print(f"表 {table}:")
            for column in columns:
                print(f"  - 字段: {column}")

        # 询问是否修复
        if len(sys.argv) > 1 and sys.argv[1] == '--fix':
            fix_mode = True
        else:
            fix_mode = input("\n是否要修复这些字段的精度？(y/n): ").lower() == 'y'

        if not fix_mode:
            print("未执行修复操作。")
            return

        # 执行修复
        print("\n开始修复datetime字段精度...")

        for table, columns in tables_to_fix.items():
            for column in columns:
                try:
                    # 使用ALTER TABLE语句修改字段精度
                    sql = f"ALTER TABLE {table} ALTER COLUMN {column} DATETIME2(1);"
                    db.session.execute(text(sql))
                    print(f"已修复: {table}.{column}")
                except Exception as e:
                    print(f"修复 {table}.{column} 时出错: {str(e)}")

        # 提交事务
        try:
            db.session.commit()
            print("\n所有修复已成功提交到数据库。")
        except Exception as e:
            db.session.rollback()
            print(f"\n提交修复时出错，已回滚: {str(e)}")

        print("\n检查和修复操作完成。")

if __name__ == '__main__':
    check_and_fix_datetime_precision()
