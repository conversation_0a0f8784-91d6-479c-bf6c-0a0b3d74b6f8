"""
修复models.py文件中的DATETIME列定义

此脚本用于修复models.py文件中被错误修改的DATETIME列定义。
"""

import re
import os
import shutil
from datetime import datetime

def backup_file(file_path):
    """创建文件备份"""
    backup_path = f"{file_path}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"已创建备份文件: {backup_path}")
    return backup_path

def fix_datetime_columns(file_path):
    """修复DATETIME列定义"""
    # 创建备份
    backup_file(file_path)
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复模式1: db.CAST(), nullable=X AS DATETIME2
    pattern1 = r'db\.CAST\(\),\s*nullable=(\d)\s*AS\s*DATETIME2'
    replacement1 = r'db.DateTime, nullable=\1'
    content = re.sub(pattern1, replacement1, content)
    
    # 修复模式2: db.CAST(), default=datetime.now, nullable=X AS DATETIME2
    pattern2 = r'db\.CAST\(\),\s*default=datetime\.now,\s*nullable=(\d)\s*AS\s*DATETIME2'
    replacement2 = r'db.DateTime, default=datetime.now, nullable=\1'
    content = re.sub(pattern2, replacement2, content)
    
    # 修复模式3: db.CAST(), default=datetime.now, onupdate=datetime.now, nullable=X AS DATETIME2
    pattern3 = r'db\.CAST\(\),\s*default=datetime\.now,\s*onupdate=datetime\.now,\s*nullable=(\d)\s*AS\s*DATETIME2'
    replacement3 = r'db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=\1'
    content = re.sub(pattern3, replacement3, content)
    
    # 修复模式4: db.CAST(), default=datetime.now AS DATETIME2
    pattern4 = r'db\.CAST\(\),\s*default=datetime\.now\s*AS\s*DATETIME2'
    replacement4 = r'db.DateTime, default=datetime.now'
    content = re.sub(pattern4, replacement4, content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复文件: {file_path}")

if __name__ == "__main__":
    # 修复models.py文件
    models_path = os.path.join('app', 'models.py')
    if os.path.exists(models_path):
        fix_datetime_columns(models_path)
    else:
        print(f"文件不存在: {models_path}")
