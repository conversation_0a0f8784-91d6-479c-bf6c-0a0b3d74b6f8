"""
修复 DATETIME2 字段的精度问题
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.mssql import DATETIME2

# revision identifiers, used by Alembic.
revision = 'fix_datetime_precision'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # 修复 supplier_school_relations 表的 created_at 和 updated_at 字段
    op.alter_column('supplier_school_relations', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    op.alter_column('supplier_school_relations', 'updated_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    
    # 修复 audit_logs 表的 created_at 字段
    op.alter_column('audit_logs', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    
    # 修复 suppliers 表的 created_at 和 updated_at 字段
    op.alter_column('suppliers', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    op.alter_column('suppliers', 'updated_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    
    # 修复 product_spec_parameters 表的 created_at 字段
    op.alter_column('product_spec_parameters', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    
    # 修复 delivery_inspections 表的 created_at 和 inspection_date 字段
    op.alter_column('delivery_inspections', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    op.alter_column('delivery_inspections', 'inspection_date',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    
    # 修复 delivery_item_inspections 表的 created_at 字段
    op.alter_column('delivery_item_inspections', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    
    # 修复 supplier_products 表的 created_at、updated_at 和 shelf_time 字段
    op.alter_column('supplier_products', 'created_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    op.alter_column('supplier_products', 'updated_at',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=False)
    op.alter_column('supplier_products', 'shelf_time',
                    existing_type=DATETIME2(precision=0),
                    type_=DATETIME2(precision=1),
                    nullable=True)

def downgrade():
    # 恢复 supplier_school_relations 表的 created_at 和 updated_at 字段
    op.alter_column('supplier_school_relations', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    op.alter_column('supplier_school_relations', 'updated_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    
    # 恢复 audit_logs 表的 created_at 字段
    op.alter_column('audit_logs', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    
    # 恢复 suppliers 表的 created_at 和 updated_at 字段
    op.alter_column('suppliers', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    op.alter_column('suppliers', 'updated_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    
    # 恢复 product_spec_parameters 表的 created_at 字段
    op.alter_column('product_spec_parameters', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    
    # 恢复 delivery_inspections 表的 created_at 和 inspection_date 字段
    op.alter_column('delivery_inspections', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    op.alter_column('delivery_inspections', 'inspection_date',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    
    # 恢复 delivery_item_inspections 表的 created_at 字段
    op.alter_column('delivery_item_inspections', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    
    # 恢复 supplier_products 表的 created_at、updated_at 和 shelf_time 字段
    op.alter_column('supplier_products', 'created_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    op.alter_column('supplier_products', 'updated_at',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=False)
    op.alter_column('supplier_products', 'shelf_time',
                    existing_type=DATETIME2(precision=1),
                    type_=DATETIME2(precision=0),
                    nullable=True)
