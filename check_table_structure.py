import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from app import db
from sqlalchemy import inspect, text

def get_table_columns(table_name):
    """获取表的列信息"""
    inspector = inspect(db.engine)
    columns = inspector.get_columns(table_name)
    print(f"\n表 '{table_name}' 的列信息:")
    for column in columns:
        print(f"列名: {column['name']}, 类型: {column['type']}, 可空: {not column.get('nullable', True)}")

def check_column_exists(table_name, column_name):
    """检查列是否存在"""
    inspector = inspect(db.engine)
    columns = inspector.get_columns(table_name)
    for column in columns:
        if column['name'].lower() == column_name.lower():
            print(f"列 '{column_name}' 在表 '{table_name}' 中已存在")
            return True
    print(f"列 '{column_name}' 在表 '{table_name}' 中不存在")
    return False

def get_foreign_keys(table_name):
    """获取表的外键信息"""
    inspector = inspect(db.engine)
    foreign_keys = inspector.get_foreign_keys(table_name)
    print(f"\n表 '{table_name}' 的外键信息:")
    for fk in foreign_keys:
        print(f"外键名: {fk.get('name')}, 引用表: {fk['referred_table']}, 引用列: {fk['referred_columns']}")
        print(f"本地列: {fk['constrained_columns']}")

if __name__ == "__main__":
    # 检查stock_ins表结构
    get_table_columns('stock_ins')
    check_column_exists('stock_ins', 'purchase_order_id')
    get_foreign_keys('stock_ins')
    
    # 检查stock_in_items表结构
    get_table_columns('stock_in_items')
    check_column_exists('stock_in_items', 'purchase_order_item_id')
    get_foreign_keys('stock_in_items')
    
    # 检查purchase_orders表结构
    get_table_columns('purchase_orders')
    get_foreign_keys('purchase_orders')
    
    # 检查purchase_order_items表结构
    get_table_columns('purchase_order_items')
    get_foreign_keys('purchase_order_items')
