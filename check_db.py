import os
import sys
import pyodbc

# 数据库连接信息
server = 'localhost'
database = 'StudentsCMSSP'
username = 'sa'
password = 'Passw0rd'  # 请替换为实际密码

# 连接字符串
conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

def check_table_columns(table_name):
    """检查表的列信息"""
    try:
        # 连接数据库
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # 查询表的列信息
        query = f"""
        SELECT 
            COLUMN_NAME, 
            DATA_TYPE, 
            CHARACTER_MAXIMUM_LENGTH, 
            IS_NULLABLE 
        FROM 
            INFORMATION_SCHEMA.COLUMNS 
        WHERE 
            TABLE_NAME = '{table_name}'
        ORDER BY 
            ORDINAL_POSITION
        """
        
        cursor.execute(query)
        columns = cursor.fetchall()
        
        print(f"\n表 '{table_name}' 的列信息:")
        for column in columns:
            print(f"列名: {column[0]}, 类型: {column[1]}, 长度: {column[2]}, 可空: {column[3]}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return columns
    except Exception as e:
        print(f"查询表 '{table_name}' 的列信息时出错: {str(e)}")
        return []

def check_foreign_keys(table_name):
    """检查表的外键信息"""
    try:
        # 连接数据库
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # 查询表的外键信息
        query = f"""
        SELECT 
            fk.name AS FK_NAME,
            OBJECT_NAME(fk.parent_object_id) AS TABLE_NAME,
            COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS COLUMN_NAME,
            OBJECT_NAME(fk.referenced_object_id) AS REFERENCED_TABLE_NAME,
            COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS REFERENCED_COLUMN_NAME
        FROM 
            sys.foreign_keys AS fk
        INNER JOIN 
            sys.foreign_key_columns AS fkc ON fk.OBJECT_ID = fkc.constraint_object_id
        WHERE 
            OBJECT_NAME(fk.parent_object_id) = '{table_name}'
        """
        
        cursor.execute(query)
        foreign_keys = cursor.fetchall()
        
        print(f"\n表 '{table_name}' 的外键信息:")
        for fk in foreign_keys:
            print(f"外键名: {fk[0]}, 列名: {fk[2]}, 引用表: {fk[3]}, 引用列: {fk[4]}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return foreign_keys
    except Exception as e:
        print(f"查询表 '{table_name}' 的外键信息时出错: {str(e)}")
        return []

if __name__ == "__main__":
    # 检查stock_ins表结构
    check_table_columns('stock_ins')
    check_foreign_keys('stock_ins')
    
    # 检查stock_in_items表结构
    check_table_columns('stock_in_items')
    check_foreign_keys('stock_in_items')
    
    # 检查purchase_orders表结构
    check_table_columns('purchase_orders')
    check_foreign_keys('purchase_orders')
    
    # 检查purchase_order_items表结构
    check_table_columns('purchase_order_items')
    check_foreign_keys('purchase_order_items')
