"""Add is_township_school field to AdministrativeArea model

Revision ID: add_is_township_school
Revises: 
Create Date: 2025-05-06 16:35:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_is_township_school'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 添加 is_township_school 字段到 administrative_areas 表
    op.add_column('administrative_areas', sa.Column('is_township_school', sa.<PERSON>(), nullable=False, server_default='0'))


def downgrade():
    # 删除 is_township_school 字段
    op.drop_column('administrative_areas', 'is_township_school')
