"""添加供应商与采购模块的溯源功能相关表和字段

Revision ID: ec19dce5184b
Revises: ec0b1e5c3e98
Create Date: 2025-05-05 18:27:55.061863

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ec19dce5184b'
down_revision = 'ec0b1e5c3e98'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('purchase_requisitions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('requisition_number', sa.String(length=50), nullable=False),
    sa.Column('area_id', sa.Integer(), nullable=False),
    sa.Column('requisition_date', sa.Date(), nullable=False),
    sa.Column('required_date', sa.Date(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('approved_by', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['area_id'], ['administrative_areas.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('requisition_number')
    )
    op.create_table('supplier_certificates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('supplier_id', sa.Integer(), nullable=False),
    sa.Column('certificate_type', sa.String(length=50), nullable=False),
    sa.Column('certificate_number', sa.String(length=100), nullable=False),
    sa.Column('issue_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('issuing_authority', sa.String(length=100), nullable=False),
    sa.Column('certificate_image', sa.String(length=200), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('purchase_requisition_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('requisition_id', sa.Integer(), nullable=False),
    sa.Column('ingredient_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('estimated_price', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('total_estimated_price', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('purpose', sa.String(length=200), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['ingredient_id'], ['ingredients.id'], ),
    sa.ForeignKeyConstraint(['requisition_id'], ['purchase_requisitions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('supplier_deliveries',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('delivery_number', sa.String(length=50), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('supplier_id', sa.Integer(), nullable=False),
    sa.Column('delivery_date', sa.Date(), nullable=False),
    sa.Column('carrier_name', sa.String(length=50), nullable=True),
    sa.Column('carrier_phone', sa.String(length=20), nullable=True),
    sa.Column('vehicle_number', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['order_id'], ['purchase_orders.id'], ),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('delivery_number')
    )
    op.create_table('delivery_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('delivery_id', sa.Integer(), nullable=False),
    sa.Column('order_item_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('batch_number', sa.String(length=50), nullable=False),
    sa.Column('production_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('certificate_numbers', sa.String(length=200), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['delivery_id'], ['supplier_deliveries.id'], ),
    sa.ForeignKeyConstraint(['order_item_id'], ['purchase_order_items.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['supplier_products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('purchase_order_items', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ingredient_id', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('unit', sa.String(length=20), nullable=False))
        batch_op.add_column(sa.Column('received_quantity', sa.Float(), server_default='0', nullable=True))
        batch_op.add_column(sa.Column('notes', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
        batch_op.create_foreign_key('fk_purchase_order_item_ingredient', 'ingredients', ['ingredient_id'], ['id'])

    with op.batch_alter_table('purchase_orders', schema=None) as batch_op:
        batch_op.add_column(sa.Column('order_number', sa.String(length=50), nullable=False))
        batch_op.add_column(sa.Column('requisition_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('area_id', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('expected_delivery_date', sa.Date(), nullable=True))
        batch_op.add_column(sa.Column('payment_terms', sa.String(length=200), nullable=True))
        batch_op.add_column(sa.Column('delivery_terms', sa.String(length=200), nullable=True))
        batch_op.add_column(sa.Column('approved_by', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
        batch_op.create_unique_constraint('uq_purchase_order_number', ['order_number'])
        batch_op.create_foreign_key('fk_purchase_order_approver', 'users', ['approved_by'], ['id'])
        batch_op.create_foreign_key('fk_purchase_order_area', 'administrative_areas', ['area_id'], ['id'])
        batch_op.create_foreign_key('fk_purchase_order_requisition', 'purchase_requisitions', ['requisition_id'], ['id'])

    with op.batch_alter_table('supplier_products', schema=None) as batch_op:
        batch_op.add_column(sa.Column('specification', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('quality_standard', sa.String(length=200), nullable=True))
        batch_op.add_column(sa.Column('lead_time', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('min_order_quantity', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))

    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('email', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('tax_id', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('bank_name', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('bank_account', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('suppliers', schema=None) as batch_op:
        batch_op.drop_column('updated_at')
        batch_op.drop_column('bank_account')
        batch_op.drop_column('bank_name')
        batch_op.drop_column('tax_id')
        batch_op.drop_column('email')

    with op.batch_alter_table('supplier_products', schema=None) as batch_op:
        batch_op.drop_column('updated_at')
        batch_op.drop_column('min_order_quantity')
        batch_op.drop_column('lead_time')
        batch_op.drop_column('quality_standard')
        batch_op.drop_column('specification')

    with op.batch_alter_table('purchase_orders', schema=None) as batch_op:
        batch_op.drop_constraint('fk_purchase_order_requisition', type_='foreignkey')
        batch_op.drop_constraint('fk_purchase_order_area', type_='foreignkey')
        batch_op.drop_constraint('fk_purchase_order_approver', type_='foreignkey')
        batch_op.drop_constraint('uq_purchase_order_number', type_='unique')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('approved_by')
        batch_op.drop_column('delivery_terms')
        batch_op.drop_column('payment_terms')
        batch_op.drop_column('expected_delivery_date')
        batch_op.drop_column('area_id')
        batch_op.drop_column('requisition_id')
        batch_op.drop_column('order_number')

    with op.batch_alter_table('purchase_order_items', schema=None) as batch_op:
        batch_op.drop_constraint('fk_purchase_order_item_ingredient', type_='foreignkey')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('created_at')
        batch_op.drop_column('notes')
        batch_op.drop_column('received_quantity')
        batch_op.drop_column('unit')
        batch_op.drop_column('ingredient_id')

    op.drop_table('delivery_items')
    op.drop_table('supplier_deliveries')
    op.drop_table('purchase_requisition_items')
    op.drop_table('supplier_certificates')
    op.drop_table('purchase_requisitions')
    # ### end Alembic commands ###
