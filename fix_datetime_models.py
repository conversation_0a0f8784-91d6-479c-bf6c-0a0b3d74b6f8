"""
修复所有模型中的日期时间字段，为它们添加精度参数
"""

import re
import os

def fix_datetime_in_file(file_path, precision=6):
    """修复文件中的日期时间字段"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 保存原始内容
    original_content = content
    
    # 查找所有的日期时间字段定义
    # 模式: db.Column(db.DateTime, ...)
    pattern = r'db\.Column\(db\.CAST(\s*,|\s*\( AS DATETIME2)'
    
    # 替换为带精度的版本
    # db.Column(db.DateTime, ...) -> db.Column(db.CAST(precision=6 AS DATETIME2), ...)
    # db.Column(db.CAST(... AS DATETIME2), ...) -> db.Column(db.CAST(... AS DATETIME2), ...)
    def replace_CAST(match AS DATETIME2):
        if match.group(1).strip() == '(':
            # 已经有括号，不需要修改
            return match.group(0)
        else:
            # 没有括号，添加精度参数
            return f'db.Column(db.CAST(precision={precision} AS DATETIME2)'
    
    # 应用替换
    content = re.sub(pattern, replace_datetime, content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已修复文件: {file_path}")
        return 1
    else:
        print(f"文件无需修复: {file_path}")
        return 0

def main():
    """主函数"""
    # 模型文件路径
    model_file = 'app/models.py'
    
    # 修复模型文件
    fixed = fix_datetime_in_file(model_file)
    
    if fixed:
        print(f"\n成功修复模型文件: {model_file}")
    else:
        print(f"\n模型文件无需修复: {model_file}")

if __name__ == "__main__":
    main()
