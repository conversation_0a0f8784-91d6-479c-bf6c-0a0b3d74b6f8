-- 修复日期时间精度问题
-- 这个脚本将修改所有表中的datetime2字段，将其精度设置为1

-- 获取所有表中的datetime2字段
DECLARE @sql NVARCHAR(MAX) = '';

-- 构建修改语句
SELECT @sql = @sql + 
    'IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''[dbo].[' + t.name + ']'') AND name = ''' + c.name + ''' AND system_type_id = 42) ' +
    'BEGIN ' +
    'PRINT ''修改表 ' + t.name + ' 中的字段 ' + c.name + ' 的精度为1''; ' +
    'ALTER TABLE [dbo].[' + t.name + '] ALTER COLUMN [' + c.name + '] DATETIME2(1) ' + 
    CASE WHEN c.is_nullable = 0 THEN 'NOT NULL' ELSE 'NULL' END + '; ' +
    'END; '
FROM sys.tables t
JOIN sys.columns c ON t.object_id = c.object_id
JOIN sys.types ty ON c.system_type_id = ty.system_type_id
WHERE t.is_ms_shipped = 0 -- 非系统表
AND ty.name = 'datetime2'
AND c.max_length = 8; -- datetime2(0)的长度是8字节

-- 执行动态SQL
PRINT '开始修改datetime2字段的精度...';
EXEC sp_executesql @sql;
PRINT '修改完成';
